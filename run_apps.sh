#!/bin/bash

echo "启动 SmartScope 应用程序..."

# 启动主 GUI 应用（后台运行）
echo "启动主 GUI 应用..."
cargo run -p smartscope &
GUI_PID=$!

# 等待一下让主应用启动
sleep 2

# 启动点云分析应用（后台运行）
echo "启动点云分析应用..."
cargo run -p smartscope-pointcloud &
POINTCLOUD_PID=$!

echo "两个应用程序已启动："
echo "主 GUI 应用 PID: $GUI_PID"
echo "点云分析应用 PID: $POINTCLOUD_PID"
echo ""
echo "按 Ctrl+C 停止所有应用程序"

# 等待用户中断
trap "echo '正在停止应用程序...'; kill $GUI_PID $POINTCLOUD_PID 2>/dev/null; exit" INT

# 等待两个进程
wait $GUI_PID $POINTCLOUD_PID 