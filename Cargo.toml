[workspace]
members = [
    "gui",
    "pointcloud",
    "camera",
    "config",
    "camera-config",
    "camera-viewer",
]
resolver = "2"

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "SmartScope - A Qt application built with Rust and CXX-Qt"
license = "MIT"
repository = "https://github.com/yourusername/SmartScope"
homepage = "https://github.com/yourusername/SmartScope"
documentation = "https://github.com/yourusername/SmartScope#readme"
keywords = ["qt", "rust", "cxx-qt", "qml", "gui", "smartscope"]
categories = ["gui", "api-bindings"]
readme = "README.md"

[workspace.dependencies]
cxx = "1.0"
cxx-qt = "0.7"
cxx-qt-lib = { version = "0.7", features = ["qt_qml", "qt_gui"] }
glam = "0.24"
rand = "0.8"
cxx-qt-build = "0.7"
