hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.0':
    '@babel/parser': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@esbuild/linux-arm64@0.25.8':
    '@esbuild/linux-arm64': private
  '@jridgewell/sourcemap-codec@1.5.4':
    '@jridgewell/sourcemap-codec': private
  '@rollup/rollup-linux-arm64-gnu@4.46.2':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.46.2':
    '@rollup/rollup-linux-arm64-musl': private
  '@tauri-apps/cli-linux-arm64-gnu@2.7.1':
    '@tauri-apps/cli-linux-arm64-gnu': private
  '@tauri-apps/cli-linux-arm64-musl@2.7.1':
    '@tauri-apps/cli-linux-arm64-musl': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@volar/language-core@2.4.15':
    '@volar/language-core': private
  '@volar/source-map@2.4.15':
    '@volar/source-map': private
  '@volar/typescript@2.4.15':
    '@volar/typescript': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/compiler-vue2@2.7.16':
    '@vue/compiler-vue2': private
  '@vue/language-core@2.2.12(typescript@5.6.3)':
    '@vue/language-core': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18(typescript@5.6.3))':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  alien-signals@1.0.13:
    alien-signals: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@2.0.2:
    brace-expansion: private
  csstype@3.1.3:
    csstype: private
  de-indent@1.0.2:
    de-indent: private
  entities@4.5.0:
    entities: private
  esbuild@0.25.8:
    esbuild: private
  estree-walker@2.0.2:
    estree-walker: private
  fdir@6.4.6(picomatch@4.0.3):
    fdir: private
  he@1.2.0:
    he: private
  magic-string@0.30.17:
    magic-string: private
  minimatch@9.0.5:
    minimatch: private
  muggle-string@0.4.1:
    muggle-string: private
  nanoid@3.3.11:
    nanoid: private
  path-browserify@1.0.1:
    path-browserify: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@4.0.3:
    picomatch: private
  postcss@8.5.6:
    postcss: private
  rollup@4.46.2:
    rollup: private
  source-map-js@1.2.1:
    source-map-js: private
  tinyglobby@0.2.14:
    tinyglobby: private
  vscode-uri@3.1.0:
    vscode-uri: private
ignoredBuilds:
  - esbuild
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Fri, 01 Aug 2025 05:27:22 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/aix-ppc64@0.25.8'
  - '@esbuild/android-arm64@0.25.8'
  - '@esbuild/android-arm@0.25.8'
  - '@esbuild/android-x64@0.25.8'
  - '@esbuild/darwin-arm64@0.25.8'
  - '@esbuild/darwin-x64@0.25.8'
  - '@esbuild/freebsd-arm64@0.25.8'
  - '@esbuild/freebsd-x64@0.25.8'
  - '@esbuild/linux-arm@0.25.8'
  - '@esbuild/linux-ia32@0.25.8'
  - '@esbuild/linux-loong64@0.25.8'
  - '@esbuild/linux-mips64el@0.25.8'
  - '@esbuild/linux-ppc64@0.25.8'
  - '@esbuild/linux-riscv64@0.25.8'
  - '@esbuild/linux-s390x@0.25.8'
  - '@esbuild/linux-x64@0.25.8'
  - '@esbuild/netbsd-arm64@0.25.8'
  - '@esbuild/netbsd-x64@0.25.8'
  - '@esbuild/openbsd-arm64@0.25.8'
  - '@esbuild/openbsd-x64@0.25.8'
  - '@esbuild/openharmony-arm64@0.25.8'
  - '@esbuild/sunos-x64@0.25.8'
  - '@esbuild/win32-arm64@0.25.8'
  - '@esbuild/win32-ia32@0.25.8'
  - '@esbuild/win32-x64@0.25.8'
  - '@rollup/rollup-android-arm-eabi@4.46.2'
  - '@rollup/rollup-android-arm64@4.46.2'
  - '@rollup/rollup-darwin-arm64@4.46.2'
  - '@rollup/rollup-darwin-x64@4.46.2'
  - '@rollup/rollup-freebsd-arm64@4.46.2'
  - '@rollup/rollup-freebsd-x64@4.46.2'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.2'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.2'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.2'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.2'
  - '@rollup/rollup-linux-riscv64-musl@4.46.2'
  - '@rollup/rollup-linux-s390x-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-gnu@4.46.2'
  - '@rollup/rollup-linux-x64-musl@4.46.2'
  - '@rollup/rollup-win32-arm64-msvc@4.46.2'
  - '@rollup/rollup-win32-ia32-msvc@4.46.2'
  - '@rollup/rollup-win32-x64-msvc@4.46.2'
  - '@tauri-apps/cli-darwin-arm64@2.7.1'
  - '@tauri-apps/cli-darwin-x64@2.7.1'
  - '@tauri-apps/cli-linux-arm-gnueabihf@2.7.1'
  - '@tauri-apps/cli-linux-riscv64-gnu@2.7.1'
  - '@tauri-apps/cli-linux-x64-gnu@2.7.1'
  - '@tauri-apps/cli-linux-x64-musl@2.7.1'
  - '@tauri-apps/cli-win32-arm64-msvc@2.7.1'
  - '@tauri-apps/cli-win32-ia32-msvc@2.7.1'
  - '@tauri-apps/cli-win32-x64-msvc@2.7.1'
  - fsevents@2.3.3
storeDir: /home/<USER>/.local/share/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
