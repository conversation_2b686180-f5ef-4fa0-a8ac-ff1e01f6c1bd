export * from './lib/codegen/globalTypes';
export * from './lib/codegen/template';
export * from './lib/languagePlugin';
export * from './lib/parsers/scriptSetupRanges';
export * from './lib/plugins';
export * from './lib/types';
export * from './lib/utils/parseSfc';
export * from './lib/utils/ts';
export * from './lib/virtualFile/vueFile';
export { tsCodegen } from './lib/plugins/vue-tsx';
export * from './lib/utils/shared';
export * from '@volar/language-core';
export type * as CompilerDOM from '@vue/compiler-dom';
