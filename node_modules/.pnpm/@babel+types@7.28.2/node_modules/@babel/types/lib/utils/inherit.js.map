{"version": 3, "names": ["inherit", "key", "child", "parent", "Array", "from", "Set", "concat", "filter", "Boolean"], "sources": ["../../src/utils/inherit.ts"], "sourcesContent": ["import type * as t from \"../index.ts\";\n\nexport default function inherit<\n  C extends t.Node | undefined,\n  P extends t.Node | undefined,\n>(key: keyof C & keyof P, child: C, parent: P): void {\n  if (child && parent) {\n    // @ts-expect-error Could further refine key definitions\n    child[key] = Array.from(\n      new Set([].concat(child[key], parent[key]).filter(Boolean)),\n    );\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,OAAOA,CAG7BC,GAAsB,EAAEC,KAAQ,EAAEC,MAAS,EAAQ;EACnD,IAAID,KAAK,IAAIC,MAAM,EAAE;IAEnBD,KAAK,CAACD,GAAG,CAAC,GAAGG,KAAK,CAACC,IAAI,CACrB,IAAIC,GAAG,CAAC,EAAE,CAACC,MAAM,CAACL,KAAK,CAACD,GAAG,CAAC,EAAEE,MAAM,CAACF,GAAG,CAAC,CAAC,CAACO,MAAM,CAACC,OAAO,CAAC,CAC5D,CAAC;EACH;AACF", "ignoreList": []}