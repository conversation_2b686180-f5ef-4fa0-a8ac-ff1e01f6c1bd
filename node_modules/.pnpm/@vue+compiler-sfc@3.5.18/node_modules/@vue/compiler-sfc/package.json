{"name": "@vue/compiler-sfc", "version": "3.5.18", "description": "@vue/compiler-sfc", "main": "dist/compiler-sfc.cjs.js", "module": "dist/compiler-sfc.esm-browser.js", "types": "dist/compiler-sfc.d.ts", "files": ["dist"], "exports": {".": {"types": "./dist/compiler-sfc.d.ts", "node": "./dist/compiler-sfc.cjs.js", "module": "./dist/compiler-sfc.esm-browser.js", "import": "./dist/compiler-sfc.esm-browser.js", "require": "./dist/compiler-sfc.cjs.js"}, "./*": "./*"}, "buildOptions": {"name": "VueCompilerSFC", "formats": ["cjs", "esm-browser"], "prod": false, "enableNonBrowserBranches": true}, "repository": {"type": "git", "url": "git+https://github.com/vuejs/core.git", "directory": "packages/compiler-sfc"}, "keywords": ["vue"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/vuejs/core/issues"}, "homepage": "https://github.com/vuejs/core/tree/main/packages/compiler-sfc#readme", "dependencies": {"@babel/parser": "^7.28.0", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1", "@vue/compiler-dom": "3.5.18", "@vue/compiler-ssr": "3.5.18", "@vue/shared": "3.5.18", "@vue/compiler-core": "3.5.18"}, "devDependencies": {"@babel/types": "^7.28.1", "@vue/consolidate": "^1.0.0", "hash-sum": "^2.0.0", "lru-cache": "10.1.0", "merge-source-map": "^1.1.0", "minimatch": "~10.0.3", "postcss-modules": "^6.0.1", "postcss-selector-parser": "^7.1.0", "pug": "^3.0.3", "sass": "^1.89.2"}}