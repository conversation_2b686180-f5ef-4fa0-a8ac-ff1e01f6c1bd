#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules/@tauri-apps/cli/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules/@tauri-apps/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules/@tauri-apps/cli/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules/@tauri-apps/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/@tauri-apps+cli@2.7.1/node_modules:/home/<USER>/App/Qt/smart-scope-rust/smartscope/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../tauri.js" "$@"
else
  exec node  "$basedir/../../tauri.js" "$@"
fi
