{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://schema.tauri.app/config/2.7.0", "title": "Config", "description": "The Tauri configuration object.\n It is read from a file where you can define your frontend assets,\n configure the bundler and define a tray icon.\n\n The configuration file is generated by the\n [`tauri init`](https://v2.tauri.app/reference/cli/#init) command that lives in\n your Tauri application source directory (src-tauri).\n\n Once generated, you may modify it at will to customize your Tauri application.\n\n ## File Formats\n\n By default, the configuration is defined as a JSON file named `tauri.conf.json`.\n\n Tauri also supports JSON5 and TOML files via the `config-json5` and `config-toml` Cargo features, respectively.\n The JSON5 file name must be either `tauri.conf.json` or `tauri.conf.json5`.\n The TOML file name is `Tauri.toml`.\n\n ## Platform-Specific Configuration\n\n In addition to the default configuration file, <PERSON><PERSON> can\n read a platform-specific configuration from `tauri.linux.conf.json`,\n `tauri.windows.conf.json`, `tauri.macos.conf.json`, `tauri.android.conf.json` and `tauri.ios.conf.json`\n (or `Tauri.linux.toml`, `Tauri.windows.toml`, `Tauri.macos.toml`, `Tauri.android.toml` and `Tauri.ios.toml` if the `Tauri.toml` format is used),\n which gets merged with the main configuration object.\n\n ## Configuration Structure\n\n The configuration is composed of the following objects:\n\n - [`app`](#appconfig): The Tauri configuration\n - [`build`](#buildconfig): The build configuration\n - [`bundle`](#bundleconfig): The bundle configurations\n - [`plugins`](#pluginconfig): The plugins configuration\n\n Example tauri.config.json file:\n\n ```json\n {\n   \"productName\": \"tauri-app\",\n   \"version\": \"0.1.0\",\n   \"build\": {\n     \"beforeBuildCommand\": \"\",\n     \"beforeDevCommand\": \"\",\n     \"devUrl\": \"http://localhost:3000\",\n     \"frontendDist\": \"../dist\"\n   },\n   \"app\": {\n     \"security\": {\n       \"csp\": null\n     },\n     \"windows\": [\n       {\n         \"fullscreen\": false,\n         \"height\": 600,\n         \"resizable\": true,\n         \"title\": \"Tauri App\",\n         \"width\": 800\n       }\n     ]\n   },\n   \"bundle\": {},\n   \"plugins\": {}\n }\n ```", "type": "object", "required": ["identifier"], "properties": {"$schema": {"description": "The JSON schema for the Tauri config.", "type": ["string", "null"]}, "productName": {"description": "App name.", "type": ["string", "null"], "pattern": "^[^/\\:*?\"<>|]+$"}, "mainBinaryName": {"description": "Overrides app's main binary filename.\n\n By default, <PERSON><PERSON> uses the output binary from `cargo`, by setting this, we will rename that binary in `tauri-cli`'s\n `tauri build` command, and target `tauri bundle` to it\n\n If possible, change the [`package name`] or set the [`name field`] instead,\n and if that's not enough and you're using nightly, consider using the [`different-binary-name`] feature instead\n\n Note: this config should not include the binary extension (e.g. `.exe`), we'll add that for you\n\n [`package name`]: https://doc.rust-lang.org/cargo/reference/manifest.html#the-name-field\n [`name field`]: https://doc.rust-lang.org/cargo/reference/cargo-targets.html#the-name-field\n [`different-binary-name`]: https://doc.rust-lang.org/nightly/cargo/reference/unstable.html#different-binary-name", "type": ["string", "null"]}, "version": {"description": "App version. It is a semver version number or a path to a `package.json` file containing the `version` field.\n\n If removed the version number from `Cargo.toml` is used.\n It's recommended to manage the app versioning in the Tauri config.\n\n ## Platform-specific\n\n - **macOS**: Translates to the bundle's CFBundleShortVersionString property and is used as the default CFBundleVersion.\n    You can set an specific bundle version using [`bundle > macOS > bundleVersion`](MacConfig::bundle_version).\n - **iOS**: Translates to the bundle's CFBundleShortVersionString property and is used as the default CFBundleVersion.\n    You can set an specific bundle version using [`bundle > iOS > bundleVersion`](IosConfig::bundle_version).\n    The `tauri ios build` CLI command has a `--build-number <number>` option that lets you append a build number to the app version.\n - **Android**: By default version 1.0 is used. You can set a version code using [`bundle > android > versionCode`](AndroidConfig::version_code).\n\n By default version 1.0 is used on Android.", "type": ["string", "null"]}, "identifier": {"description": "The application identifier in reverse domain name notation (e.g. `com.tauri.example`).\n This string must be unique across applications since it is used in system configurations like\n the bundle ID and path to the webview data directory.\n This string must contain only alphanumeric characters (A-Z, a-z, and 0-9), hyphens (-),\n and periods (.).", "type": "string"}, "app": {"description": "The App configuration.", "default": {"enableGTKAppId": false, "macOSPrivateApi": false, "security": {"assetProtocol": {"enable": false, "scope": []}, "capabilities": [], "dangerousDisableAssetCspModification": false, "freezePrototype": false, "pattern": {"use": "brownfield"}}, "windows": [], "withGlobalTauri": false}, "allOf": [{"$ref": "#/definitions/AppConfig"}]}, "build": {"description": "The build configuration.", "default": {"removeUnusedCommands": false}, "allOf": [{"$ref": "#/definitions/BuildConfig"}]}, "bundle": {"description": "The bundler configuration.", "default": {"active": false, "android": {"minSdkVersion": 24}, "createUpdaterArtifacts": false, "iOS": {"minimumSystemVersion": "13.0"}, "icon": [], "linux": {"appimage": {"bundleMediaFramework": false, "files": {}}, "deb": {"files": {}}, "rpm": {"epoch": 0, "files": {}, "release": "1"}}, "macOS": {"dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "files": {}, "hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "targets": "all", "useLocalToolsDir": false, "windows": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "signCommand": null, "timestampUrl": null, "tsp": false, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}}, "allOf": [{"$ref": "#/definitions/BundleConfig"}]}, "plugins": {"description": "The plugins config.", "default": {}, "allOf": [{"$ref": "#/definitions/PluginConfig"}]}}, "additionalProperties": false, "definitions": {"AppConfig": {"description": "The App configuration object.\n\n See more: <https://v2.tauri.app/reference/config/#appconfig>", "type": "object", "properties": {"windows": {"description": "The app windows configuration.", "default": [], "type": "array", "items": {"$ref": "#/definitions/WindowConfig"}}, "security": {"description": "Security configuration.", "default": {"assetProtocol": {"enable": false, "scope": []}, "capabilities": [], "dangerousDisableAssetCspModification": false, "freezePrototype": false, "pattern": {"use": "brownfield"}}, "allOf": [{"$ref": "#/definitions/SecurityConfig"}]}, "trayIcon": {"description": "Configuration for app tray icon.", "anyOf": [{"$ref": "#/definitions/TrayIconConfig"}, {"type": "null"}]}, "macOSPrivateApi": {"description": "MacOS private API configuration. Enables the transparent background API and sets the `fullScreenEnabled` preference to `true`.", "default": false, "type": "boolean"}, "withGlobalTauri": {"description": "Whether we should inject the Tauri API on `window.__TAURI__` or not.", "default": false, "type": "boolean"}, "enableGTKAppId": {"description": "If set to true \"identifier\" will be set as GTK app ID (on systems that use GTK).", "default": false, "type": "boolean"}}, "additionalProperties": false}, "WindowConfig": {"description": "The window configuration object.\n\n See more: <https://v2.tauri.app/reference/config/#windowconfig>", "type": "object", "properties": {"label": {"description": "The window identifier. It must be alphanumeric.", "default": "main", "type": "string"}, "create": {"description": "Whether <PERSON><PERSON> should create this window at app startup or not.\n\n When this is set to `false` you must manually grab the config object via `app.config().app.windows`\n and create it with [`WebviewWindowBuilder::from_config`](https://docs.rs/tauri/2/tauri/webview/struct.WebviewWindowBuilder.html#method.from_config).", "default": true, "type": "boolean"}, "url": {"description": "The window webview URL.", "default": "index.html", "allOf": [{"$ref": "#/definitions/WebviewUrl"}]}, "userAgent": {"description": "The user agent for the webview", "type": ["string", "null"]}, "dragDropEnabled": {"description": "Whether the drag and drop is enabled or not on the webview. By default it is enabled.\n\n Disabling it is required to use HTML5 drag and drop on the frontend on Windows.", "default": true, "type": "boolean"}, "center": {"description": "Whether or not the window starts centered or not.", "default": false, "type": "boolean"}, "x": {"description": "The horizontal position of the window's top left corner", "type": ["number", "null"], "format": "double"}, "y": {"description": "The vertical position of the window's top left corner", "type": ["number", "null"], "format": "double"}, "width": {"description": "The window width.", "default": 800.0, "type": "number", "format": "double"}, "height": {"description": "The window height.", "default": 600.0, "type": "number", "format": "double"}, "minWidth": {"description": "The min window width.", "type": ["number", "null"], "format": "double"}, "minHeight": {"description": "The min window height.", "type": ["number", "null"], "format": "double"}, "maxWidth": {"description": "The max window width.", "type": ["number", "null"], "format": "double"}, "maxHeight": {"description": "The max window height.", "type": ["number", "null"], "format": "double"}, "preventOverflow": {"description": "Whether or not to prevent the window from overflowing the workarea\n\n ## Platform-specific\n\n - **iOS / Android:** Unsupported.", "anyOf": [{"$ref": "#/definitions/PreventOverflowConfig"}, {"type": "null"}]}, "resizable": {"description": "Whether the window is resizable or not. When resizable is set to false, native window's maximize button is automatically disabled.", "default": true, "type": "boolean"}, "maximizable": {"description": "Whether the window's native maximize button is enabled or not.\n If resizable is set to false, this setting is ignored.\n\n ## Platform-specific\n\n - **macOS:** Disables the \"zoom\" button in the window titlebar, which is also used to enter fullscreen mode.\n - **Linux / iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "minimizable": {"description": "Whether the window's native minimize button is enabled or not.\n\n ## Platform-specific\n\n - **Linux / iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "closable": {"description": "Whether the window's native close button is enabled or not.\n\n ## Platform-specific\n\n - **Linux:** \"GTK+ will do its best to convince the window manager not to show a close button.\n   Depending on the system, this function may not have any effect when called on a window that is already visible\"\n - **iOS / Android:** Unsupported.", "default": true, "type": "boolean"}, "title": {"description": "The window title.", "default": "<PERSON><PERSON>", "type": "string"}, "fullscreen": {"description": "Whether the window starts as fullscreen or not.", "default": false, "type": "boolean"}, "focus": {"description": "Whether the window will be initially focused or not.", "default": true, "type": "boolean"}, "transparent": {"description": "Whether the window is transparent or not.\n\n Note that on `macOS` this requires the `macos-private-api` feature flag, enabled under `tauri > macOSPrivateApi`.\n WARNING: Using private APIs on `macOS` prevents your application from being accepted to the `App Store`.", "default": false, "type": "boolean"}, "maximized": {"description": "Whether the window is maximized or not.", "default": false, "type": "boolean"}, "visible": {"description": "Whether the window is visible or not.", "default": true, "type": "boolean"}, "decorations": {"description": "Whether the window should have borders and bars.", "default": true, "type": "boolean"}, "alwaysOnBottom": {"description": "Whether the window should always be below other windows.", "default": false, "type": "boolean"}, "alwaysOnTop": {"description": "Whether the window should always be on top of other windows.", "default": false, "type": "boolean"}, "visibleOnAllWorkspaces": {"description": "Whether the window should be visible on all workspaces or virtual desktops.\n\n ## Platform-specific\n\n - **Windows / iOS / Android:** Unsupported.", "default": false, "type": "boolean"}, "contentProtected": {"description": "Prevents the window contents from being captured by other apps.", "default": false, "type": "boolean"}, "skipTaskbar": {"description": "If `true`, hides the window icon from the taskbar on Windows and Linux.", "default": false, "type": "boolean"}, "windowClassname": {"description": "The name of the window class created on Windows to create the window. **Windows only**.", "type": ["string", "null"]}, "theme": {"description": "The initial window theme. Defaults to the system theme. Only implemented on Windows and macOS 10.14+.", "anyOf": [{"$ref": "#/definitions/Theme"}, {"type": "null"}]}, "titleBarStyle": {"description": "The style of the macOS title bar.", "default": "Visible", "allOf": [{"$ref": "#/definitions/TitleBarStyle"}]}, "trafficLightPosition": {"description": "The position of the window controls on macOS.\n\n Requires titleBarStyle: Overlay and decorations: true.", "anyOf": [{"$ref": "#/definitions/LogicalPosition"}, {"type": "null"}]}, "hiddenTitle": {"description": "If `true`, sets the window title to be hidden on macOS.", "default": false, "type": "boolean"}, "acceptFirstMouse": {"description": "Whether clicking an inactive window also clicks through to the webview on macOS.", "default": false, "type": "boolean"}, "tabbingIdentifier": {"description": "Defines the window [tabbing identifier] for macOS.\n\n Windows with matching tabbing identifiers will be grouped together.\n If the tabbing identifier is not set, automatic tabbing will be disabled.\n\n [tabbing identifier]: <https://developer.apple.com/documentation/appkit/nswindow/1644704-tabbingidentifier>", "type": ["string", "null"]}, "additionalBrowserArgs": {"description": "Defines additional browser arguments on Windows. By default wry passes `--disable-features=msWebOOUI,msPdfOOUI,msSmartScreenProtection`\n so if you use this method, you also need to disable these components by yourself if you want.", "type": ["string", "null"]}, "shadow": {"description": "Whether or not the window has shadow.\n\n ## Platform-specific\n\n - **Windows:**\n   - `false` has no effect on decorated window, shadow are always ON.\n   - `true` will make undecorated window have a 1px white border,\n and on Windows 11, it will have a rounded corners.\n - **Linux:** Unsupported.", "default": true, "type": "boolean"}, "windowEffects": {"description": "Window effects.\n\n Requires the window to be transparent.\n\n ## Platform-specific:\n\n - **Windows**: If using decorations or shadows, you may want to try this workaround <https://github.com/tauri-apps/tao/issues/72#issuecomment-975607891>\n - **Linux**: Unsupported", "anyOf": [{"$ref": "#/definitions/WindowEffectsConfig"}, {"type": "null"}]}, "incognito": {"description": "Whether or not the webview should be launched in incognito  mode.\n\n  ## Platform-specific:\n\n  - **Android**: Unsupported.", "default": false, "type": "boolean"}, "parent": {"description": "Sets the window associated with this label to be the parent of the window to be created.\n\n ## Platform-specific\n\n - **Windows**: This sets the passed parent as an owner window to the window to be created.\n   From [MSDN owned windows docs](https://docs.microsoft.com/en-us/windows/win32/winmsg/window-features#owned-windows):\n     - An owned window is always above its owner in the z-order.\n     - The system automatically destroys an owned window when its owner is destroyed.\n     - An owned window is hidden when its owner is minimized.\n - **Linux**: This makes the new window transient for parent, see <https://docs.gtk.org/gtk3/method.Window.set_transient_for.html>\n - **macOS**: This adds the window as a child of parent, see <https://developer.apple.com/documentation/appkit/nswindow/1419152-addchildwindow?language=objc>", "type": ["string", "null"]}, "proxyUrl": {"description": "The proxy URL for the WebView for all network requests.\n\n Must be either a `http://` or a `socks5://` URL.\n\n ## Platform-specific\n\n - **macOS**: Requires the `macos-proxy` feature flag and only compiles for macOS 14+.", "type": ["string", "null"], "format": "uri"}, "zoomHotkeysEnabled": {"description": "Whether page zooming by hotkeys is enabled\n\n ## Platform-specific:\n\n - **Windows**: Controls WebView2's [`IsZoomControlEnabled`](https://learn.microsoft.com/en-us/microsoft-edge/webview2/reference/winrt/microsoft_web_webview2_core/corewebview2settings?view=webview2-winrt-1.0.2420.47#iszoomcontrolenabled) setting.\n - **MacOS / Linux**: Injects a polyfill that zooms in and out with `ctrl/command` + `-/=`,\n 20% in each step, ranging from 20% to 1000%. Requires `webview:allow-set-webview-zoom` permission\n\n - **Android / iOS**: Unsupported.", "default": false, "type": "boolean"}, "browserExtensionsEnabled": {"description": "Whether browser extensions can be installed for the webview process\n\n ## Platform-specific:\n\n - **Windows**: Enables the WebView2 environment's [`AreBrowserExtensionsEnabled`](https://learn.microsoft.com/en-us/microsoft-edge/webview2/reference/winrt/microsoft_web_webview2_core/corewebview2environmentoptions?view=webview2-winrt-1.0.2739.15#arebrowserextensionsenabled)\n - **MacOS / Linux / iOS / Android** - Unsupported.", "default": false, "type": "boolean"}, "useHttpsScheme": {"description": "Sets whether the custom protocols should use `https://<scheme>.localhost` instead of the default `http://<scheme>.localhost` on Windows and Android. Defaults to `false`.\n\n ## Note\n\n Using a `https` scheme will NOT allow mixed content when trying to fetch `http` endpoints and therefore will not match the behavior of the `<scheme>://localhost` protocols used on macOS and Linux.\n\n ## Warning\n\n Changing this value between releases will change the IndexedDB, cookies and localstorage location and your app will not be able to access the old data.", "default": false, "type": "boolean"}, "devtools": {"description": "Enable web inspector which is usually called browser devtools. Enabled by default.\n\n This API works in **debug** builds, but requires `devtools` feature flag to enable it in **release** builds.\n\n ## Platform-specific\n\n - macOS: This will call private functions on **macOS**.\n - Android: Open `chrome://inspect/#devices` in Chrome to get the devtools window. <PERSON><PERSON>'s `WebView` devtools API isn't supported on Android.\n - iOS: Open Safari > Develop > [Your Device Name] > [Your WebView] to get the devtools window.", "type": ["boolean", "null"]}, "backgroundColor": {"description": "Set the window and webview background color.\n\n ## Platform-specific:\n\n - **Windows**: alpha channel is ignored for the window layer.\n - **Windows**: On Windows 7, alpha channel is ignored for the webview layer.\n - **Windows**: On Windows 8 and newer, if alpha channel is not `0`, it will be ignored for the webview layer.", "anyOf": [{"$ref": "#/definitions/Color"}, {"type": "null"}]}, "backgroundThrottling": {"description": "Change the default background throttling behaviour.\n\n By default, browsers use a suspend policy that will throttle timers and even unload\n the whole tab (view) to free resources after roughly 5 minutes when a view became\n minimized or hidden. This will pause all tasks until the documents visibility state\n changes back from hidden to visible by bringing the view back to the foreground.\n\n ## Platform-specific\n\n - **Linux / Windows / Android**: Unsupported. Workarounds like a pending WebLock transaction might suffice.\n - **iOS**: Supported since version 17.0+.\n - **macOS**: Supported since version 14.0+.\n\n see https://github.com/tauri-apps/tauri/issues/5250#issuecomment-2569380578", "anyOf": [{"$ref": "#/definitions/BackgroundThrottlingPolicy"}, {"type": "null"}]}, "javascriptDisabled": {"description": "Whether we should disable JavaScript code execution on the webview or not.", "default": false, "type": "boolean"}, "allowLinkPreview": {"description": "on macOS and iOS there is a link preview on long pressing links, this is enabled by default.\n see https://docs.rs/objc2-web-kit/latest/objc2_web_kit/struct.WKWebView.html#method.allowsLinkPreview", "default": true, "type": "boolean"}, "disableInputAccessoryView": {"description": "Allows disabling the input accessory view on iOS.\n\n The accessory view is the view that appears above the keyboard when a text input element is focused.\n It usually displays a view with \"Done\", \"Next\" buttons.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "WebviewUrl": {"description": "An URL to open on a Tauri webview window.", "anyOf": [{"description": "An external URL. Must use either the `http` or `https` schemes.", "type": "string", "format": "uri"}, {"description": "The path portion of an app URL.\n For instance, to load `tauri://localhost/users/john`,\n you can simply provide `users/john` in this configuration.", "type": "string"}, {"description": "A custom protocol url, for example, `doom://index.html`", "type": "string", "format": "uri"}]}, "PreventOverflowConfig": {"description": "Prevent overflow with a margin", "anyOf": [{"description": "Enable prevent overflow or not", "type": "boolean"}, {"description": "Enable prevent overflow with a margin\n so that the window's size + this margin won't overflow the workarea", "allOf": [{"$ref": "#/definitions/PreventOverflowMargin"}]}]}, "PreventOverflowMargin": {"description": "Enable prevent overflow with a margin\n so that the window's size + this margin won't overflow the workarea", "type": "object", "required": ["height", "width"], "properties": {"width": {"description": "Horizontal margin in physical unit", "type": "integer", "format": "uint32", "minimum": 0.0}, "height": {"description": "Vertical margin in physical unit", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, "Theme": {"description": "System theme.", "oneOf": [{"description": "Light theme.", "type": "string", "enum": ["Light"]}, {"description": "Dark theme.", "type": "string", "enum": ["Dark"]}]}, "TitleBarStyle": {"description": "How the window title bar should be displayed on macOS.", "oneOf": [{"description": "A normal title bar.", "type": "string", "enum": ["Visible"]}, {"description": "Makes the title bar transparent, so the window background color is shown instead.\n\n Useful if you don't need to have actual HTML under the title bar. This lets you avoid the caveats of using `TitleBarStyle::Overlay`. Will be more useful when <PERSON><PERSON> lets you set a custom window background color.", "type": "string", "enum": ["Transparent"]}, {"description": "Shows the title bar as a transparent overlay over the window's content.\n\n Keep in mind:\n - The height of the title bar is different on different OS versions, which can lead to window the controls and title not being where you don't expect.\n - You need to define a custom drag region to make your window draggable, however due to a limitation you can't drag the window when it's not in focus <https://github.com/tauri-apps/tauri/issues/4316>.\n - The color of the window title depends on the system theme.", "type": "string", "enum": ["Overlay"]}]}, "LogicalPosition": {"description": "Position coordinates struct.", "type": "object", "required": ["x", "y"], "properties": {"x": {"description": "X coordinate.", "type": "number", "format": "double"}, "y": {"description": "Y coordinate.", "type": "number", "format": "double"}}, "additionalProperties": false}, "WindowEffectsConfig": {"description": "The window effects configuration object", "type": "object", "required": ["effects"], "properties": {"effects": {"description": "List of Window effects to apply to the Window.\n Conflicting effects will apply the first one and ignore the rest.", "type": "array", "items": {"$ref": "#/definitions/WindowEffect"}}, "state": {"description": "Window effect state **macOS Only**", "anyOf": [{"$ref": "#/definitions/WindowEffectState"}, {"type": "null"}]}, "radius": {"description": "Window effect corner radius **macOS Only**", "type": ["number", "null"], "format": "double"}, "color": {"description": "Window effect color. Affects [`WindowEffect::Blur`] and [`WindowEffect::Acrylic`] only\n on Windows 10 v1903+. Doesn't have any effect on Windows 7 or Windows 11.", "anyOf": [{"$ref": "#/definitions/Color"}, {"type": "null"}]}}, "additionalProperties": false}, "WindowEffect": {"description": "Platform-specific window effects", "oneOf": [{"description": "A default material appropriate for the view's effectiveAppearance. **macOS 10.14-**", "deprecated": true, "type": "string", "enum": ["appearanceBased"]}, {"description": "**macOS 10.14-**", "deprecated": true, "type": "string", "enum": ["light"]}, {"description": "**macOS 10.14-**", "deprecated": true, "type": "string", "enum": ["dark"]}, {"description": "**macOS 10.14-**", "deprecated": true, "type": "string", "enum": ["mediumLight"]}, {"description": "**macOS 10.14-**", "deprecated": true, "type": "string", "enum": ["ultraDark"]}, {"description": "**macOS 10.10+**", "type": "string", "enum": ["titlebar"]}, {"description": "**macOS 10.10+**", "type": "string", "enum": ["selection"]}, {"description": "**macOS 10.11+**", "type": "string", "enum": ["menu"]}, {"description": "**macOS 10.11+**", "type": "string", "enum": ["popover"]}, {"description": "**macOS 10.11+**", "type": "string", "enum": ["sidebar"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["headerView"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["sheet"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["windowBackground"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["<PERSON>ud<PERSON><PERSON><PERSON>"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["fullScreenUI"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["tooltip"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["contentBackground"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["underWindowBackground"]}, {"description": "**macOS 10.14+**", "type": "string", "enum": ["underPageBackground"]}, {"description": "Mica effect that matches the system dark perefence **Windows 11 Only**", "type": "string", "enum": ["mica"]}, {"description": "Mica effect with dark mode but only if dark mode is enabled on the system **Windows 11 Only**", "type": "string", "enum": ["micaDark"]}, {"description": "Mica effect with light mode **Windows 11 Only**", "type": "string", "enum": ["micaLight"]}, {"description": "Tabbed effect that matches the system dark perefence **Windows 11 Only**", "type": "string", "enum": ["tabbed"]}, {"description": "Tabbed effect with dark mode but only if dark mode is enabled on the system **Windows 11 Only**", "type": "string", "enum": ["tabbedDark"]}, {"description": "Tabbed effect with light mode **Windows 11 Only**", "type": "string", "enum": ["tabbedLight"]}, {"description": "**Windows 7/10/11(22H1) Only**\n\n ## Notes\n\n This effect has bad performance when resizing/dragging the window on Windows 11 build 22621.", "type": "string", "enum": ["blur"]}, {"description": "**Windows 10/11 Only**\n\n ## Notes\n\n This effect has bad performance when resizing/dragging the window on Windows 10 v1903+ and Windows 11 build 22000.", "type": "string", "enum": ["acrylic"]}]}, "WindowEffectState": {"description": "Window effect state **macOS only**\n\n <https://developer.apple.com/documentation/appkit/nsvisualeffectview/state>", "oneOf": [{"description": "Make window effect state follow the window's active state", "type": "string", "enum": ["followsWindowActiveState"]}, {"description": "Make window effect state always active", "type": "string", "enum": ["active"]}, {"description": "Make window effect state always inactive", "type": "string", "enum": ["inactive"]}]}, "Color": {"anyOf": [{"description": "Color hex string, for example: #fff, #ffffff, or #ffffffff.", "type": "string", "pattern": "^#?([A-Fa-f0-9]{3}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$"}, {"description": "Array of RGB colors. Each value has minimum of 0 and maximum of 255.", "type": "array", "items": [{"type": "integer", "format": "uint8", "minimum": 0.0}, {"type": "integer", "format": "uint8", "minimum": 0.0}, {"type": "integer", "format": "uint8", "minimum": 0.0}], "maxItems": 3, "minItems": 3}, {"description": "Array of RGBA colors. Each value has minimum of 0 and maximum of 255.", "type": "array", "items": [{"type": "integer", "format": "uint8", "minimum": 0.0}, {"type": "integer", "format": "uint8", "minimum": 0.0}, {"type": "integer", "format": "uint8", "minimum": 0.0}, {"type": "integer", "format": "uint8", "minimum": 0.0}], "maxItems": 4, "minItems": 4}, {"description": "Object of red, green, blue, alpha color values. Each value has minimum of 0 and maximum of 255.", "type": "object", "required": ["blue", "green", "red"], "properties": {"red": {"type": "integer", "format": "uint8", "minimum": 0.0}, "green": {"type": "integer", "format": "uint8", "minimum": 0.0}, "blue": {"type": "integer", "format": "uint8", "minimum": 0.0}, "alpha": {"default": 255, "type": "integer", "format": "uint8", "minimum": 0.0}}}]}, "BackgroundThrottlingPolicy": {"description": "Background throttling policy.", "oneOf": [{"description": "A policy where background throttling is disabled", "type": "string", "enum": ["disabled"]}, {"description": "A policy where a web view that’s not in a window fully suspends tasks. This is usually the default behavior in case no policy is set.", "type": "string", "enum": ["suspend"]}, {"description": "A policy where a web view that’s not in a window limits processing, but does not fully suspend tasks.", "type": "string", "enum": ["throttle"]}]}, "SecurityConfig": {"description": "Security configuration.\n\n See more: <https://v2.tauri.app/reference/config/#securityconfig>", "type": "object", "properties": {"csp": {"description": "The Content Security Policy that will be injected on all HTML files on the built application.\n If [`dev_csp`](#SecurityConfig.devCsp) is not specified, this value is also injected on dev.\n\n This is a really important part of the configuration since it helps you ensure your WebView is secured.\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"$ref": "#/definitions/Csp"}, {"type": "null"}]}, "devCsp": {"description": "The Content Security Policy that will be injected on all HTML files on development.\n\n This is a really important part of the configuration since it helps you ensure your WebView is secured.\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"$ref": "#/definitions/Csp"}, {"type": "null"}]}, "freezePrototype": {"description": "Freeze the `Object.prototype` when using the custom protocol.", "default": false, "type": "boolean"}, "dangerousDisableAssetCspModification": {"description": "Disables the Tauri-injected CSP sources.\n\n At compile time, <PERSON><PERSON> parses all the frontend assets and changes the Content-Security-Policy\n to only allow loading of your own scripts and styles by injecting nonce and hash sources.\n This stricts your CSP, which may introduce issues when using along with other flexing sources.\n\n This configuration option allows both a boolean and a list of strings as value.\n A boolean instructs <PERSON><PERSON> to disable the injection for all CSP injections,\n and a list of strings indicates the CSP directives that <PERSON><PERSON> cannot inject.\n\n **WARNING:** Only disable this if you know what you are doing and have properly configured the CSP.\n Your application might be vulnerable to XSS attacks without this <PERSON><PERSON> protection.", "default": false, "allOf": [{"$ref": "#/definitions/DisabledCspModificationKind"}]}, "assetProtocol": {"description": "Custom protocol config.", "default": {"enable": false, "scope": []}, "allOf": [{"$ref": "#/definitions/AssetProtocolConfig"}]}, "pattern": {"description": "The pattern to use.", "default": {"use": "brownfield"}, "allOf": [{"$ref": "#/definitions/PatternKind"}]}, "capabilities": {"description": "List of capabilities that are enabled on the application.\n\n If the list is empty, all capabilities are included.", "default": [], "type": "array", "items": {"$ref": "#/definitions/CapabilityEntry"}}, "headers": {"description": "The headers, which are added to every http response from tauri to the web view\n This doesn't include IPC Messages and error responses", "anyOf": [{"$ref": "#/definitions/HeaderConfig"}, {"type": "null"}]}}, "additionalProperties": false}, "Csp": {"description": "A Content-Security-Policy definition.\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP>.", "anyOf": [{"description": "The entire CSP policy in a single text string.", "type": "string"}, {"description": "An object mapping a directive with its sources values as a list of strings.", "type": "object", "additionalProperties": {"$ref": "#/definitions/CspDirectiveSources"}}]}, "CspDirectiveSources": {"description": "A Content-Security-Policy directive source list.\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Security-Policy/Sources#sources>.", "anyOf": [{"description": "An inline list of CSP sources. Same as [`Self::List`], but concatenated with a space separator.", "type": "string"}, {"description": "A list of CSP sources. The collection will be concatenated with a space separator for the CSP string.", "type": "array", "items": {"type": "string"}}]}, "DisabledCspModificationKind": {"description": "The possible values for the `dangerous_disable_asset_csp_modification` config option.", "anyOf": [{"description": "If `true`, disables all CSP modification.\n `false` is the default value and it configures <PERSON><PERSON> to control the CSP.", "type": "boolean"}, {"description": "Disables the given list of CSP directives modifications.", "type": "array", "items": {"type": "string"}}]}, "AssetProtocolConfig": {"description": "Config for the asset custom protocol.\n\n See more: <https://v2.tauri.app/reference/config/#assetprotocolconfig>", "type": "object", "properties": {"scope": {"description": "The access scope for the asset protocol.", "default": [], "allOf": [{"$ref": "#/definitions/FsScope"}]}, "enable": {"description": "Enables the asset protocol.", "default": false, "type": "boolean"}}, "additionalProperties": false}, "FsScope": {"description": "Protocol scope definition.\n It is a list of glob patterns that restrict the API access from the webview.\n\n Each pattern can start with a variable that resolves to a system base directory.\n The variables are: `$AUDIO`, `$CACHE`, `$CONFIG`, `$DATA`, `$LOCALDATA`, `$DESKTOP`,\n `$DOCUMENT`, `$DOWNLOAD`, `$EXE`, `$FONT`, `$HOME`, `$PICTURE`, `$PUBLIC`, `$RUNTIME`,\n `$TEMPLATE`, `$VIDEO`, `$RESOURCE`, `$APP`, `$LOG`, `$TEMP`, `$APPCONFIG`, `$APPDATA`,\n `$APPLOCALDATA`, `$APPCACHE`, `$APPLOG`.", "anyOf": [{"description": "A list of paths that are allowed by this scope.", "type": "array", "items": {"type": "string"}}, {"description": "A complete scope configuration.", "type": "object", "properties": {"allow": {"description": "A list of paths that are allowed by this scope.", "default": [], "type": "array", "items": {"type": "string"}}, "deny": {"description": "A list of paths that are not allowed by this scope.\n This gets precedence over the [`Self::Scope::allow`] list.", "default": [], "type": "array", "items": {"type": "string"}}, "requireLiteralLeadingDot": {"description": "Whether or not paths that contain components that start with a `.`\n will require that `.` appears literally in the pattern; `*`, `?`, `**`,\n or `[...]` will not match. This is useful because such files are\n conventionally considered hidden on Unix systems and it might be\n desirable to skip them when listing files.\n\n Defaults to `true` on Unix systems and `false` on Windows", "type": ["boolean", "null"]}}}]}, "PatternKind": {"description": "The application pattern.", "oneOf": [{"description": "Brownfield pattern.", "type": "object", "required": ["use"], "properties": {"use": {"type": "string", "enum": ["brownfield"]}}}, {"description": "Isolation pattern. Recommended for security purposes.", "type": "object", "required": ["options", "use"], "properties": {"use": {"type": "string", "enum": ["isolation"]}, "options": {"type": "object", "required": ["dir"], "properties": {"dir": {"description": "The dir containing the index.html file that contains the secure isolation application.", "type": "string"}}}}}]}, "CapabilityEntry": {"description": "A capability entry which can be either an inlined capability or a reference to a capability defined on its own file.", "anyOf": [{"description": "An inlined capability.", "allOf": [{"$ref": "#/definitions/Capability"}]}, {"description": "Reference to a capability identifier.", "type": "string"}]}, "Capability": {"description": "A grouping and boundary mechanism developers can use to isolate access to the IPC layer.\n\n It controls application windows' and webviews' fine grained access\n to the Tauri core, application, or plugin commands.\n If a webview or its window is not matching any capability then it has no access to the IPC layer at all.\n\n This can be done to create groups of windows, based on their required system access, which can reduce\n impact of frontend vulnerabilities in less privileged windows.\n Windows can be added to a capability by exact name (e.g. `main-window`) or glob patterns like `*` or `admin-*`.\n A Window can have none, one, or multiple associated capabilities.\n\n ## Example\n\n ```json\n {\n   \"identifier\": \"main-user-files-write\",\n   \"description\": \"This capability allows the `main` window on macOS and Windows access to `filesystem` write related commands and `dialog` commands to enable programmatic access to files selected by the user.\",\n   \"windows\": [\n     \"main\"\n   ],\n   \"permissions\": [\n     \"core:default\",\n     \"dialog:open\",\n     {\n       \"identifier\": \"fs:allow-write-text-file\",\n       \"allow\": [{ \"path\": \"$HOME/test.txt\" }]\n     },\n   ],\n   \"platforms\": [\"macOS\",\"windows\"]\n }\n ```", "type": "object", "required": ["identifier", "permissions"], "properties": {"identifier": {"description": "Identifier of the capability.\n\n ## Example\n\n `main-user-files-write`", "type": "string"}, "description": {"description": "Description of what the capability is intended to allow on associated windows.\n\n It should contain a description of what the grouped permissions should allow.\n\n ## Example\n\n This capability allows the `main` window access to `filesystem` write related\n commands and `dialog` commands to enable programmatic access to files selected by the user.", "default": "", "type": "string"}, "remote": {"description": "Configure remote URLs that can use the capability permissions.\n\n This setting is optional and defaults to not being set, as our\n default use case is that the content is served from our local application.\n\n :::caution\n Make sure you understand the security implications of providing remote\n sources with local system access.\n :::\n\n ## Example\n\n ```json\n {\n   \"urls\": [\"https://*.mydomain.dev\"]\n }\n ```", "anyOf": [{"$ref": "#/definitions/CapabilityRemote"}, {"type": "null"}]}, "local": {"description": "Whether this capability is enabled for local app URLs or not. Defaults to `true`.", "default": true, "type": "boolean"}, "windows": {"description": "List of windows that are affected by this capability. Can be a glob pattern.\n\n If a window label matches any of the patterns in this list,\n the capability will be enabled on all the webviews of that window,\n regardless of the value of [`Self::webviews`].\n\n On multiwebview windows, prefer specifying [`Self::webviews`] and omitting [`Self::windows`]\n for a fine grained access control.\n\n ## Example\n\n `[\"main\"]`", "type": "array", "items": {"type": "string"}}, "webviews": {"description": "List of webviews that are affected by this capability. Can be a glob pattern.\n\n The capability will be enabled on all the webviews\n whose label matches any of the patterns in this list,\n regardless of whether the webview's window label matches a pattern in [`Self::windows`].\n\n ## Example\n\n `[\"sub-webview-one\", \"sub-webview-two\"]`", "type": "array", "items": {"type": "string"}}, "permissions": {"description": "List of permissions attached to this capability.\n\n Must include the plugin name as prefix in the form of `${plugin-name}:${permission-name}`.\n For commands directly implemented in the application itself only `${permission-name}`\n is required.\n\n ## Example\n\n ```json\n [\n   \"core:default\",\n   \"shell:allow-open\",\n   \"dialog:open\",\n   {\n     \"identifier\": \"fs:allow-write-text-file\",\n     \"allow\": [{ \"path\": \"$HOME/test.txt\" }]\n   }\n ]\n ```", "type": "array", "items": {"$ref": "#/definitions/PermissionEntry"}, "uniqueItems": true}, "platforms": {"description": "Limit which target platforms this capability applies to.\n\n By default all platforms are targeted.\n\n ## Example\n\n `[\"macOS\",\"windows\"]`", "type": ["array", "null"], "items": {"$ref": "#/definitions/Target"}}}}, "CapabilityRemote": {"description": "Configuration for remote URLs that are associated with the capability.", "type": "object", "required": ["urls"], "properties": {"urls": {"description": "Remote domains this capability refers to using the [URLPattern standard](https://urlpattern.spec.whatwg.org/).\n\n ## Examples\n\n - \"https://*.mydomain.dev\": allows subdomains of mydomain.dev\n - \"https://mydomain.dev/api/*\": allows any subpath of mydomain.dev/api", "type": "array", "items": {"type": "string"}}}}, "PermissionEntry": {"description": "An entry for a permission value in a [`Capability`] can be either a raw permission [`Identifier`]\n or an object that references a permission and extends its scope.", "anyOf": [{"description": "Reference a permission or permission set by identifier.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, {"description": "Reference a permission or permission set by identifier and extends its scope.", "type": "object", "required": ["identifier"], "properties": {"identifier": {"description": "Identifier of the permission or permission set.", "allOf": [{"$ref": "#/definitions/Identifier"}]}, "allow": {"description": "Data that defines what is allowed by the scope.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}, "deny": {"description": "Data that defines what is denied by the scope. This should be prioritized by validation logic.", "type": ["array", "null"], "items": {"$ref": "#/definitions/Value"}}}}]}, "Identifier": {"type": "string"}, "Value": {"description": "All supported ACL values.", "anyOf": [{"description": "Represents a null JSON value.", "type": "null"}, {"description": "Represents a [`bool`].", "type": "boolean"}, {"description": "Represents a valid ACL [`Number`].", "allOf": [{"$ref": "#/definitions/Number"}]}, {"description": "Represents a [`String`].", "type": "string"}, {"description": "Represents a list of other [`Value`]s.", "type": "array", "items": {"$ref": "#/definitions/Value"}}, {"description": "Represents a map of [`String`] keys to [`Value`]s.", "type": "object", "additionalProperties": {"$ref": "#/definitions/Value"}}]}, "Number": {"description": "A valid ACL number.", "anyOf": [{"description": "Represents an [`i64`].", "type": "integer", "format": "int64"}, {"description": "Represents a [`f64`].", "type": "number", "format": "double"}]}, "Target": {"description": "Platform target.", "oneOf": [{"description": "MacOS.", "type": "string", "enum": ["macOS"]}, {"description": "Windows.", "type": "string", "enum": ["windows"]}, {"description": "Linux.", "type": "string", "enum": ["linux"]}, {"description": "Android.", "type": "string", "enum": ["android"]}, {"description": "iOS.", "type": "string", "enum": ["iOS"]}]}, "HeaderConfig": {"description": "A struct, where the keys are some specific http header names.\n\n If the values to those keys are defined, then they will be send as part of a response message.\n This does not include error messages and ipc messages\n\n ## Example configuration\n ```javascript\n {\n  //..\n   app:{\n     //..\n     security: {\n       headers: {\n         \"Cross-Origin-Opener-Policy\": \"same-origin\",\n         \"Cross-Origin-Embedder-Policy\": \"require-corp\",\n         \"Timing-Allow-Origin\": [\n           \"https://developer.mozilla.org\",\n           \"https://example.com\",\n         ],\n         \"Access-Control-Expose-Headers\": \"Tauri-Custom-Header\",\n         \"Tauri-Custom-Header\": {\n           \"key1\": \"'value1' 'value2'\",\n           \"key2\": \"'value3'\"\n         }\n       },\n       csp: \"default-src 'self'; connect-src ipc: http://ipc.localhost\",\n     }\n     //..\n   }\n  //..\n }\n ```\n In this example `Cross-Origin-Opener-Policy` and `Cross-Origin-Embedder-Policy` are set to allow for the use of [`SharedArrayBuffer`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/SharedArrayBuffer).\n The result is, that those headers are then set on every response sent via the `get_response` function in crates/tauri/src/protocol/tauri.rs.\n The Content-Security-Policy header is defined separately, because it is also handled separately.\n\n For the helloworld example, this config translates into those response headers:\n ```http\n access-control-allow-origin:  http://tauri.localhost\n access-control-expose-headers: Tauri-Custom-Header\n content-security-policy: default-src 'self'; connect-src ipc: http://ipc.localhost; script-src 'self' 'sha256-Wjjrs6qinmnr+tOry8x8PPwI77eGpUFR3EEGZktjJNs='\n content-type: text/html\n cross-origin-embedder-policy: require-corp\n cross-origin-opener-policy: same-origin\n tauri-custom-header: key1 'value1' 'value2'; key2 'value3'\n timing-allow-origin: https://developer.mozilla.org, https://example.com\n ```\n Since the resulting header values are always 'string-like'. So depending on the what data type the HeaderSource is, they need to be converted.\n  - `String`(JS/Rust): stay the same for the resulting header value\n  - `Array`(JS)/`Vec\\<String\\>`(Rust): Item are joined by \", \" for the resulting header value\n  - `Object`(JS)/ `Hashmap\\<String,String\\>`(Rust): Items are composed from: key + space + value. Item are then joined by \"; \" for the resulting header value", "type": "object", "properties": {"Access-Control-Allow-Credentials": {"description": "The Access-Control-Allow-Credentials response header tells browsers whether the\n server allows cross-origin HTTP requests to include credentials.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Credentials>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Access-Control-Allow-Headers": {"description": "The Access-Control-Allow-Headers response header is used in response\n to a preflight request which includes the Access-Control-Request-Headers\n to indicate which HTTP headers can be used during the actual request.\n\n This header is required if the request has an Access-Control-Request-Headers header.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Headers>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Access-Control-Allow-Methods": {"description": "The Access-Control-Allow-Methods response header specifies one or more methods\n allowed when accessing a resource in response to a preflight request.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Allow-Methods>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Access-Control-Expose-Headers": {"description": "The Access-Control-Expose-Headers response header allows a server to indicate\n which response headers should be made available to scripts running in the browser,\n in response to a cross-origin request.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose-Headers>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Access-Control-Max-Age": {"description": "The Access-Control-Max-Age response header indicates how long the results of a\n preflight request (that is the information contained in the\n Access-Control-Allow-Methods and Access-Control-Allow-Headers headers) can\n be cached.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Max-Age>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Cross-Origin-Embedder-Policy": {"description": "The HTTP Cross-Origin-Embedder-Policy (COEP) response header configures embedding\n cross-origin resources into the document.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Embedder-Policy>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Cross-Origin-Opener-Policy": {"description": "The HTTP Cross-Origin-Opener-Policy (COOP) response header allows you to ensure a\n top-level document does not share a browsing context group with cross-origin documents.\n COOP will process-isolate your document and potential attackers can't access your global\n object if they were to open it in a popup, preventing a set of cross-origin attacks dubbed XS-Leaks.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Opener-Policy>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Cross-Origin-Resource-Policy": {"description": "The HTTP Cross-Origin-Resource-Policy response header conveys a desire that the\n browser blocks no-cors cross-origin/cross-site requests to the given resource.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Cross-Origin-Resource-Policy>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Permissions-Policy": {"description": "The HTTP Permissions-Policy header provides a mechanism to allow and deny the\n use of browser features in a document or within any \\<iframe\\> elements in the document.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Permissions-Policy>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Service-Worker-Allowed": {"description": "The HTTP Service-Worker-Allowed response header is used to broaden the path restriction for a\n service worker's default scope.\n\n By default, the scope for a service worker registration is the directory where the service\n worker script is located. For example, if the script `sw.js` is located in `/js/sw.js`,\n it can only control URLs under `/js/` by default. Servers can use the `Service-Worker-Allowed`\n header to allow a service worker to control URLs outside of its own directory.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Reference/Headers/Service-Worker-Allowed>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Timing-Allow-Origin": {"description": "The Timing-Allow-Origin response header specifies origins that are allowed to see values\n of attributes retrieved via features of the Resource Timing API, which would otherwise be\n reported as zero due to cross-origin restrictions.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Timing-Allow-Origin>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "X-Content-Type-Options": {"description": "The X-Content-Type-Options response HTTP header is a marker used by the server to indicate\n that the MIME types advertised in the Content-Type headers should be followed and not be\n changed. The header allows you to avoid MIME type sniffing by saying that the MIME types\n are deliberately configured.\n\n See <https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/X-Content-Type-Options>", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}, "Tauri-Custom-Header": {"description": "A custom header field Tauri-Custom-Header, don't use it.\n Remember to set Access-Control-Expose-Headers accordingly\n\n **NOT INTENDED FOR PRODUCTION USE**", "anyOf": [{"$ref": "#/definitions/HeaderSource"}, {"type": "null"}]}}, "additionalProperties": false}, "HeaderSource": {"description": "definition of a header source\n\n The header value to a header name", "anyOf": [{"description": "string version of the header Value", "type": "string"}, {"description": "list version of the header value. Item are joined by \",\" for the real header value", "type": "array", "items": {"type": "string"}}, {"description": "(Rust struct | Json | JavaScript Object) equivalent of the header value. Items are composed from: key + space + value. Item are then joined by \";\" for the real header value", "type": "object", "additionalProperties": {"type": "string"}}]}, "TrayIconConfig": {"description": "Configuration for application tray icon.\n\n See more: <https://v2.tauri.app/reference/config/#trayiconconfig>", "type": "object", "required": ["iconPath"], "properties": {"id": {"description": "Set an id for this tray icon so you can reference it later, defaults to `main`.", "type": ["string", "null"]}, "iconPath": {"description": "Path to the default icon to use for the tray icon.\n\n Note: this stores the image in raw pixels to the final binary,\n so keep the icon size (width and height) small\n or else it's going to bloat your final executable", "type": "string"}, "iconAsTemplate": {"description": "A Boolean value that determines whether the image represents a [template](https://developer.apple.com/documentation/appkit/nsimage/1520017-template?language=objc) image on macOS.", "default": false, "type": "boolean"}, "menuOnLeftClick": {"description": "A Boolean value that determines whether the menu should appear when the tray icon receives a left click.\n\n ## Platform-specific:\n\n - **Linux**: Unsupported.", "default": true, "deprecated": true, "type": "boolean"}, "showMenuOnLeftClick": {"description": "A Boolean value that determines whether the menu should appear when the tray icon receives a left click.\n\n ## Platform-specific:\n\n - **Linux**: Unsupported.", "default": true, "type": "boolean"}, "title": {"description": "Title for MacOS tray", "type": ["string", "null"]}, "tooltip": {"description": "Tray icon tooltip on Windows and macOS", "type": ["string", "null"]}}, "additionalProperties": false}, "BuildConfig": {"description": "The Build configuration object.\n\n See more: <https://v2.tauri.app/reference/config/#buildconfig>", "type": "object", "properties": {"runner": {"description": "The binary used to build and run the application.", "anyOf": [{"$ref": "#/definitions/RunnerConfig"}, {"type": "null"}]}, "devUrl": {"description": "The URL to load in development.\n\n This is usually an URL to a dev server, which serves your application assets with hot-reload and HMR.\n Most modern JavaScript bundlers like [Vite](https://vite.dev/guide/) provides a way to start a dev server by default.\n\n If you don't have a dev server or don't want to use one, ignore this option and use [`frontendDist`](BuildConfig::frontend_dist)\n and point to a web assets directory, and Tauri CLI will run its built-in dev server and provide a simple hot-reload experience.", "type": ["string", "null"], "format": "uri"}, "frontendDist": {"description": "The path to the application assets (usually the `dist` folder of your javascript bundler)\n or a URL that could be either a custom protocol registered in the tauri app (for example: `myprotocol://`)\n or a remote URL (for example: `https://site.com/app`).\n\n When a path relative to the configuration file is provided,\n it is read recursively and all files are embedded in the application binary.\n <PERSON><PERSON> then looks for an `index.html` and serves it as the default entry point for your application.\n\n You can also provide a list of paths to be embedded, which allows granular control over what files are added to the binary.\n In this case, all files are added to the root and you must reference it that way in your HTML files.\n\n When a URL is provided, the application won't have bundled assets\n and the application will load that URL by default.", "anyOf": [{"$ref": "#/definitions/FrontendDist"}, {"type": "null"}]}, "beforeDevCommand": {"description": "A shell command to run before `tauri dev` kicks in.\n\n The TAURI_ENV_PLATFORM, TAURI_ENV_ARCH, TAURI_ENV_FAMILY, TAURI_ENV_PLATFORM_VERSION, TAURI_ENV_PLATFORM_TYPE and TAURI_ENV_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/BeforeDevCommand"}, {"type": "null"}]}, "beforeBuildCommand": {"description": "A shell command to run before `tauri build` kicks in.\n\n The TAURI_ENV_PLATFORM, TAURI_ENV_ARCH, TAURI_ENV_FAMILY, TAURI_ENV_PLATFORM_VERSION, TAURI_ENV_PLATFORM_TYPE and TAURI_ENV_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/HookCommand"}, {"type": "null"}]}, "beforeBundleCommand": {"description": "A shell command to run before the bundling phase in `tauri build` kicks in.\n\n The TAURI_ENV_PLATFORM, TAURI_ENV_ARCH, TAURI_ENV_FAMILY, TAURI_ENV_PLATFORM_VERSION, TAURI_ENV_PLATFORM_TYPE and TAURI_ENV_DEBUG environment variables are set if you perform conditional compilation.", "anyOf": [{"$ref": "#/definitions/HookCommand"}, {"type": "null"}]}, "features": {"description": "Features passed to `cargo` commands.", "type": ["array", "null"], "items": {"type": "string"}}, "removeUnusedCommands": {"description": "Try to remove unused commands registered from plugins base on the ACL list during `tauri build`,\n the way it works is that tauri-cli will read this and set the environment variables for the build script and macros,\n and they'll try to get all the allowed commands and remove the rest\n\n Note:\n   - This won't be accounting for dynamically added ACLs when you use features from the `dynamic-acl` (currently enabled by default) feature flag, so make sure to check it when using this\n   - This feature requires tauri-plugin 2.1 and tauri 2.4", "default": false, "type": "boolean"}}, "additionalProperties": false}, "RunnerConfig": {"description": "The runner configuration.", "anyOf": [{"description": "A string specifying the binary to run.", "type": "string"}, {"description": "An object with advanced configuration options.", "type": "object", "required": ["cmd"], "properties": {"cmd": {"description": "The binary to run.", "type": "string"}, "cwd": {"description": "The current working directory to run the command from.", "type": ["string", "null"]}, "args": {"description": "Arguments to pass to the command.", "type": ["array", "null"], "items": {"type": "string"}}}}]}, "FrontendDist": {"description": "Defines the URL or assets to embed in the application.", "anyOf": [{"description": "An external URL that should be used as the default application URL.", "type": "string", "format": "uri"}, {"description": "Path to a directory containing the frontend dist assets.", "type": "string"}, {"description": "An array of files to embed on the app.", "type": "array", "items": {"type": "string"}}]}, "BeforeDevCommand": {"description": "Describes the shell command to run before `tauri dev`.", "anyOf": [{"description": "Run the given script with the default options.", "type": "string"}, {"description": "Run the given script with custom options.", "type": "object", "required": ["script"], "properties": {"script": {"description": "The script to execute.", "type": "string"}, "cwd": {"description": "The current working directory.", "type": ["string", "null"]}, "wait": {"description": "Whether `tauri dev` should wait for the command to finish or not. Defaults to `false`.", "default": false, "type": "boolean"}}}]}, "HookCommand": {"description": "Describes a shell command to be executed when a CLI hook is triggered.", "anyOf": [{"description": "Run the given script with the default options.", "type": "string"}, {"description": "Run the given script with custom options.", "type": "object", "required": ["script"], "properties": {"script": {"description": "The script to execute.", "type": "string"}, "cwd": {"description": "The current working directory.", "type": ["string", "null"]}}}]}, "BundleConfig": {"description": "Configuration for tauri-bundler.\n\n See more: <https://v2.tauri.app/reference/config/#bundleconfig>", "type": "object", "properties": {"active": {"description": "Whether <PERSON><PERSON> should bundle your application or just output the executable.", "default": false, "type": "boolean"}, "targets": {"description": "The bundle targets, currently supports [\"deb\", \"rpm\", \"appimage\", \"nsis\", \"msi\", \"app\", \"dmg\"] or \"all\".", "default": "all", "allOf": [{"$ref": "#/definitions/BundleTarget"}]}, "createUpdaterArtifacts": {"description": "Produce updaters and their signatures or not", "default": false, "allOf": [{"$ref": "#/definitions/Updater"}]}, "publisher": {"description": "The application's publisher. Defaults to the second element in the identifier string.\n\n Currently maps to the Manufacturer property of the Windows Installer\n and the Maintainer field of debian packages if the Cargo.toml does not have the authors field.", "type": ["string", "null"]}, "homepage": {"description": "A url to the home page of your application. If unset, will\n fallback to `homepage` defined in `Cargo.toml`.\n\n Supported bundle targets: `deb`, `rpm`, `nsis` and `msi`.", "type": ["string", "null"]}, "icon": {"description": "The app's icons", "default": [], "type": "array", "items": {"type": "string"}}, "resources": {"description": "App resources to bundle.\n Each resource is a path to a file or directory.\n Glob patterns are supported.", "anyOf": [{"$ref": "#/definitions/BundleResources"}, {"type": "null"}]}, "copyright": {"description": "A copyright string associated with your application.", "type": ["string", "null"]}, "license": {"description": "The package's license identifier to be included in the appropriate bundles.\n If not set, defaults to the license from the Cargo.toml file.", "type": ["string", "null"]}, "licenseFile": {"description": "The path to the license file to be included in the appropriate bundles.", "type": ["string", "null"]}, "category": {"description": "The application kind.\n\n Should be one of the following:\n Business, DeveloperTool, Education, Entertainment, Finance, Game, ActionGame, AdventureGame, ArcadeGame, BoardGame, CardGame, CasinoGame, DiceGame, EducationalGame, FamilyGame, KidsGame, MusicGame, PuzzleGame, RacingGame, RolePlayingGame, SimulationGame, SportsGame, StrategyGame, TriviaGame, WordGame, GraphicsAndDesign, HealthcareAndFitness, Lifestyle, Medical, Music, News, Photography, Productivity, Reference, SocialNetworking, Sports, Travel, Utility, Video, Weather.", "type": ["string", "null"]}, "fileAssociations": {"description": "File associations to application.", "type": ["array", "null"], "items": {"$ref": "#/definitions/FileAssociation"}}, "shortDescription": {"description": "A short description of your application.", "type": ["string", "null"]}, "longDescription": {"description": "A longer, multi-line description of the application.", "type": ["string", "null"]}, "useLocalToolsDir": {"description": "Whether to use the project's `target` directory, for caching build tools (e.g., Wix and NSIS) when building this application. Defaults to `false`.\n\n If true, tools will be cached in `target/.tauri/`.\n If false, tools will be cached in the current user's platform-specific cache directory.\n\n An example where it can be appropriate to set this to `true` is when building this application as a Windows System user (e.g., AWS EC2 workloads),\n because the Window system's app data directory is restricted.", "default": false, "type": "boolean"}, "externalBin": {"description": "A list of—either absolute or relative—paths to binaries to embed with your application.\n\n Note that <PERSON><PERSON> will look for system-specific binaries following the pattern \"binary-name{-target-triple}{.system-extension}\".\n\n E.g. for the external binary \"my-binary\", <PERSON><PERSON> looks for:\n\n - \"my-binary-x86_64-pc-windows-msvc.exe\" for Windows\n - \"my-binary-x86_64-apple-darwin\" for macOS\n - \"my-binary-x86_64-unknown-linux-gnu\" for Linux\n\n so don't forget to provide binaries for all targeted platforms.", "type": ["array", "null"], "items": {"type": "string"}}, "windows": {"description": "Configuration for the Windows bundles.", "default": {"allowDowngrades": true, "certificateThumbprint": null, "digestAlgorithm": null, "nsis": null, "signCommand": null, "timestampUrl": null, "tsp": false, "webviewInstallMode": {"silent": true, "type": "downloadBootstrapper"}, "wix": null}, "allOf": [{"$ref": "#/definitions/WindowsConfig"}]}, "linux": {"description": "Configuration for the Linux bundles.", "default": {"appimage": {"bundleMediaFramework": false, "files": {}}, "deb": {"files": {}}, "rpm": {"epoch": 0, "files": {}, "release": "1"}}, "allOf": [{"$ref": "#/definitions/LinuxConfig"}]}, "macOS": {"description": "Configuration for the macOS bundles.", "default": {"dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "files": {}, "hardenedRuntime": true, "minimumSystemVersion": "10.13"}, "allOf": [{"$ref": "#/definitions/MacConfig"}]}, "iOS": {"description": "iOS configuration.", "default": {"minimumSystemVersion": "13.0"}, "allOf": [{"$ref": "#/definitions/IosConfig"}]}, "android": {"description": "Android configuration.", "default": {"minSdkVersion": 24}, "allOf": [{"$ref": "#/definitions/AndroidConfig"}]}}, "additionalProperties": false}, "BundleTarget": {"description": "Targets to bundle. Each value is case insensitive.", "anyOf": [{"description": "Bundle all targets.", "const": "all"}, {"description": "A list of bundle targets.", "type": "array", "items": {"$ref": "#/definitions/BundleType"}}, {"description": "A single bundle target.", "allOf": [{"$ref": "#/definitions/BundleType"}]}]}, "BundleType": {"description": "A bundle referenced by tauri-bundler.", "oneOf": [{"description": "The debian bundle (.deb).", "type": "string", "enum": ["deb"]}, {"description": "The RPM bundle (.rpm).", "type": "string", "enum": ["rpm"]}, {"description": "The AppImage bundle (.appimage).", "type": "string", "enum": ["appimage"]}, {"description": "The Microsoft Installer bundle (.msi).", "type": "string", "enum": ["msi"]}, {"description": "The NSIS bundle (.exe).", "type": "string", "enum": ["nsis"]}, {"description": "The macOS application bundle (.app).", "type": "string", "enum": ["app"]}, {"description": "The Apple Disk Image bundle (.dmg).", "type": "string", "enum": ["dmg"]}]}, "Updater": {"description": "Updater type", "anyOf": [{"description": "Generates legacy zipped v1 compatible updaters", "allOf": [{"$ref": "#/definitions/V1Compatible"}]}, {"description": "Produce updaters and their signatures or not", "type": "boolean"}]}, "V1Compatible": {"description": "Generates legacy zipped v1 compatible updaters", "oneOf": [{"description": "Generates legacy zipped v1 compatible updaters", "type": "string", "enum": ["v1Compatible"]}]}, "BundleResources": {"description": "Definition for bundle resources.\n Can be either a list of paths to include or a map of source to target paths.", "anyOf": [{"description": "A list of paths to include.", "type": "array", "items": {"type": "string"}}, {"description": "A map of source to target paths.", "type": "object", "additionalProperties": {"type": "string"}}]}, "FileAssociation": {"description": "File association", "type": "object", "required": ["ext"], "properties": {"ext": {"description": "File extensions to associate with this app. e.g. 'png'", "type": "array", "items": {"$ref": "#/definitions/AssociationExt"}}, "name": {"description": "The name. Maps to `CFBundleTypeName` on macOS. Default to `ext[0]`", "type": ["string", "null"]}, "description": {"description": "The association description. Windows-only. It is displayed on the `Type` column on Windows Explorer.", "type": ["string", "null"]}, "role": {"description": "The app's role with respect to the type. Maps to `CFBundleTypeRole` on macOS.", "default": "Editor", "allOf": [{"$ref": "#/definitions/BundleTypeRole"}]}, "mimeType": {"description": "The mime-type e.g. 'image/png' or 'text/plain'. Linux-only.", "type": ["string", "null"]}, "rank": {"description": "The ranking of this app among apps that declare themselves as editors or viewers of the given file type.  Maps to `LSHandlerRank` on macOS.", "default": "<PERSON><PERSON><PERSON>", "allOf": [{"$ref": "#/definitions/HandlerRank"}]}}, "additionalProperties": false}, "AssociationExt": {"description": "An extension for a [`FileAssociation`].\n\n A leading `.` is automatically stripped.", "type": "string"}, "BundleTypeRole": {"description": "macOS-only. Corresponds to CFBundleTypeRole", "oneOf": [{"description": "CFBundleTypeRole.Editor. Files can be read and edited.", "type": "string", "enum": ["Editor"]}, {"description": "CFBundleTypeRole.Viewer. Files can be read.", "type": "string", "enum": ["Viewer"]}, {"description": "CFBundleTypeRole.Shell", "type": "string", "enum": ["Shell"]}, {"description": "CFBundleTypeRole.QLGenerator", "type": "string", "enum": ["QLGenerator"]}, {"description": "CFBundleTypeRole.None", "type": "string", "enum": ["None"]}]}, "HandlerRank": {"description": "Corresponds to LSHandlerRank", "oneOf": [{"description": "LSHandlerRank.Default. This app is an opener of files of this type; this value is also used if no rank is specified.", "type": "string", "enum": ["<PERSON><PERSON><PERSON>"]}, {"description": "LSHandlerRank.Owner. This app is the primary creator of files of this type.", "type": "string", "enum": ["Owner"]}, {"description": "LSHandlerRank.Alternate. This app is a secondary viewer of files of this type.", "type": "string", "enum": ["Alternate"]}, {"description": "LSHandlerRank.None. This app is never selected to open files of this type, but it accepts drops of files of this type.", "type": "string", "enum": ["None"]}]}, "WindowsConfig": {"description": "Windows bundler configuration.\n\n See more: <https://v2.tauri.app/reference/config/#windowsconfig>", "type": "object", "properties": {"digestAlgorithm": {"description": "Specifies the file digest algorithm to use for creating file signatures.\n Required for code signing. SHA-256 is recommended.", "type": ["string", "null"]}, "certificateThumbprint": {"description": "Specifies the SHA1 hash of the signing certificate.", "type": ["string", "null"]}, "timestampUrl": {"description": "Server to use during timestamping.", "type": ["string", "null"]}, "tsp": {"description": "Whether to use Time-Stamp Protocol (TSP, a.k.a. RFC 3161) for the timestamp server. Your code signing provider may\n use a TSP timestamp server, like e.g. SSL.com does. If so, enable TSP by setting to true.", "default": false, "type": "boolean"}, "webviewInstallMode": {"description": "The installation mode for the Webview2 runtime.", "default": {"silent": true, "type": "downloadBootstrapper"}, "allOf": [{"$ref": "#/definitions/WebviewInstallMode"}]}, "allowDowngrades": {"description": "Validates a second app installation, blocking the user from installing an older version if set to `false`.\n\n For instance, if `1.2.1` is installed, the user won't be able to install app version `1.2.0` or `1.1.5`.\n\n The default value of this flag is `true`.", "default": true, "type": "boolean"}, "wix": {"description": "Configuration for the MSI generated with WiX.", "anyOf": [{"$ref": "#/definitions/WixConfig"}, {"type": "null"}]}, "nsis": {"description": "Configuration for the installer generated with NSIS.", "anyOf": [{"$ref": "#/definitions/NsisConfig"}, {"type": "null"}]}, "signCommand": {"description": "Specify a custom command to sign the binaries.\n This command needs to have a `%1` in args which is just a placeholder for the binary path,\n which we will detect and replace before calling the command.\n\n By Default we use `signtool.exe` which can be found only on Windows so\n if you are on another platform and want to cross-compile and sign you will\n need to use another tool like `osslsigncode`.", "anyOf": [{"$ref": "#/definitions/CustomSignCommandConfig"}, {"type": "null"}]}}, "additionalProperties": false}, "WebviewInstallMode": {"description": "Install modes for the Webview2 runtime.\n Note that for the updater bundle [`Self::DownloadBootstrapper`] is used.\n\n For more information see <https://v2.tauri.app/distribute/windows-installer/#webview2-installation-options>.", "oneOf": [{"description": "Do not install the Webview2 as part of the Windows Installer.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["skip"]}}, "additionalProperties": false}, {"description": "Download the bootstrapper and run it.\n Requires an internet connection.\n Results in a smaller installer size, but is not recommended on Windows 7.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["downloadBootstrapper"]}, "silent": {"description": "Instructs the installer to run the bootstrapper in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed the bootstrapper and run it.\n Requires an internet connection.\n Increases the installer size by around 1.8MB, but offers better support on Windows 7.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["embed<PERSON><PERSON><PERSON><PERSON>"]}, "silent": {"description": "Instructs the installer to run the bootstrapper in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed the offline installer and run it.\n Does not require an internet connection.\n Increases the installer size by around 127MB.", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["offlineInstaller"]}, "silent": {"description": "Instructs the installer to run the installer in silent mode. Defaults to `true`.", "default": true, "type": "boolean"}}, "additionalProperties": false}, {"description": "Embed a fixed webview2 version and use it at runtime.\n Increases the installer size by around 180MB.", "type": "object", "required": ["path", "type"], "properties": {"type": {"type": "string", "enum": ["fixedRuntime"]}, "path": {"description": "The path to the fixed runtime to use.\n\n The fixed version can be downloaded [on the official website](https://developer.microsoft.com/en-us/microsoft-edge/webview2/#download-section).\n The `.cab` file must be extracted to a folder and this folder path must be defined on this field.", "type": "string"}}, "additionalProperties": false}]}, "WixConfig": {"description": "Configuration for the MSI bundle using WiX.\n\n See more: <https://v2.tauri.app/reference/config/#wixconfig>", "type": "object", "properties": {"version": {"description": "MSI installer version in the format `major.minor.patch.build` (build is optional).\n\n Because a valid version is required for MSI installer, it will be derived from [`Config::version`] if this field is not set.\n\n The first field is the major version and has a maximum value of 255. The second field is the minor version and has a maximum value of 255.\n The third and foruth fields have a maximum value of 65,535.\n\n See <https://learn.microsoft.com/en-us/windows/win32/msi/productversion> for more info.", "type": ["string", "null"]}, "upgradeCode": {"description": "A GUID upgrade code for MSI installer. This code **_must stay the same across all of your updates_**,\n otherwise, Windows will treat your update as a different app and your users will have duplicate versions of your app.\n\n By default, tauri generates this code by generating a Uuid v5 using the string `<productName>.exe.app.x64` in the DNS namespace.\n You can use <PERSON><PERSON>'s CLI to generate and print this code for you, run `tauri inspect wix-upgrade-code`.\n\n It is recommended that you set this value in your tauri config file to avoid accidental changes in your upgrade code\n whenever you want to change your product name.", "type": ["string", "null"], "format": "uuid"}, "language": {"description": "The installer languages to build. See <https://docs.microsoft.com/en-us/windows/win32/msi/localizing-the-error-and-actiontext-tables>.", "default": "en-US", "allOf": [{"$ref": "#/definitions/WixLanguage"}]}, "template": {"description": "A custom .wxs template to use.", "type": ["string", "null"]}, "fragmentPaths": {"description": "A list of paths to .wxs files with WiX fragments to use.", "default": [], "type": "array", "items": {"type": "string"}}, "componentGroupRefs": {"description": "The ComponentGroup element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "componentRefs": {"description": "The Component element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "featureGroupRefs": {"description": "The FeatureGroup element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "featureRefs": {"description": "The Feature element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "mergeRefs": {"description": "The Merge element ids you want to reference from the fragments.", "default": [], "type": "array", "items": {"type": "string"}}, "enableElevatedUpdateTask": {"description": "Create an elevated update task within Windows Task Scheduler.", "default": false, "type": "boolean"}, "bannerPath": {"description": "Path to a bitmap file to use as the installation user interface banner.\n This bitmap will appear at the top of all but the first page of the installer.\n\n The required dimensions are 493px × 58px.", "type": ["string", "null"]}, "dialogImagePath": {"description": "Path to a bitmap file to use on the installation user interface dialogs.\n It is used on the welcome and completion dialogs.\n\n The required dimensions are 493px × 312px.", "type": ["string", "null"]}}, "additionalProperties": false}, "WixLanguage": {"description": "The languages to build using WiX.", "anyOf": [{"description": "A single language to build, without configuration.", "type": "string"}, {"description": "A list of languages to build, without configuration.", "type": "array", "items": {"type": "string"}}, {"description": "A map of languages and its configuration.", "type": "object", "additionalProperties": {"$ref": "#/definitions/WixLanguageConfig"}}]}, "WixLanguageConfig": {"description": "Configuration for a target language for the WiX build.\n\n See more: <https://v2.tauri.app/reference/config/#wixlanguageconfig>", "type": "object", "properties": {"localePath": {"description": "The path to a locale (`.wxl`) file. See <https://wixtoolset.org/documentation/manual/v3/howtos/ui_and_localization/build_a_localized_version.html>.", "type": ["string", "null"]}}, "additionalProperties": false}, "NsisConfig": {"description": "Configuration for the Installer bundle using NSIS.", "type": "object", "properties": {"template": {"description": "A custom .nsi template to use.", "type": ["string", "null"]}, "headerImage": {"description": "The path to a bitmap file to display on the header of installers pages.\n\n The recommended dimensions are 150px x 57px.", "type": ["string", "null"]}, "sidebarImage": {"description": "The path to a bitmap file for the Welcome page and the Finish page.\n\n The recommended dimensions are 164px x 314px.", "type": ["string", "null"]}, "installerIcon": {"description": "The path to an icon file used as the installer icon.", "type": ["string", "null"]}, "installMode": {"description": "Whether the installation will be for all users or just the current user.", "default": "currentUser", "allOf": [{"$ref": "#/definitions/NSISInstallerMode"}]}, "languages": {"description": "A list of installer languages.\n By default the OS language is used. If the OS language is not in the list of languages, the first language will be used.\n To allow the user to select the language, set `display_language_selector` to `true`.\n\n See <https://github.com/kichik/nsis/tree/9465c08046f00ccb6eda985abbdbf52c275c6c4d/Contrib/Language%20files> for the complete list of languages.", "type": ["array", "null"], "items": {"type": "string"}}, "customLanguageFiles": {"description": "A key-value pair where the key is the language and the\n value is the path to a custom `.nsh` file that holds the translated text for tauri's custom messages.\n\n See <https://github.com/tauri-apps/tauri/blob/dev/crates/tauri-bundler/src/bundle/windows/nsis/languages/English.nsh> for an example `.nsh` file.\n\n **Note**: the key must be a valid NSIS language and it must be added to [`NsisConfig`] languages array,", "type": ["object", "null"], "additionalProperties": {"type": "string"}}, "displayLanguageSelector": {"description": "Whether to display a language selector dialog before the installer and uninstaller windows are rendered or not.\n By default the OS language is selected, with a fallback to the first language in the `languages` array.", "default": false, "type": "boolean"}, "compression": {"description": "Set the compression algorithm used to compress files in the installer.\n\n See <https://nsis.sourceforge.io/Reference/SetCompressor>", "default": "lzma", "allOf": [{"$ref": "#/definitions/NsisCompression"}]}, "startMenuFolder": {"description": "Set the folder name for the start menu shortcut.\n\n Use this option if you have multiple apps and wish to group their shortcuts under one folder\n or if you generally prefer to set your shortcut inside a folder.\n\n Examples:\n - `AwesomePublisher`, shortcut will be placed in `%AppData%\\Microsoft\\Windows\\Start Menu\\Programs\\AwesomePublisher\\<your-app>.lnk`\n - If unset, shortcut will be placed in `%AppData%\\Microsoft\\Windows\\Start Menu\\Programs\\<your-app>.lnk`", "type": ["string", "null"]}, "installerHooks": {"description": "A path to a `.nsh` file that contains special NSIS macros to be hooked into the\n main installer.nsi script.\n\n Supported hooks are:\n - `NSIS_HOOK_PREINSTALL`: This hook runs before copying files, setting registry key values and creating shortcuts.\n - `NSIS_HOOK_POSTINSTALL`: This hook runs after the installer has finished copying all files, setting the registry keys and created shortcuts.\n - `NSIS_HOOK_PREUNINSTALL`: This hook runs before removing any files, registry keys and shortcuts.\n - `NSIS_HOOK_POSTUNINSTALL`: This hook runs after files, registry keys and shortcuts have been removed.\n\n\n ### Example\n\n ```nsh\n !macro NSIS_HOOK_PREINSTALL\n   MessageBox MB_OK \"PreInstall\"\n !macroend\n\n !macro NSIS_HOOK_POSTINSTALL\n   MessageBox MB_OK \"PostInstall\"\n !macroend\n\n !macro NSIS_HOOK_PREUNINSTALL\n   MessageBox MB_OK \"PreUnInstall\"\n !macroend\n\n !macro NSIS_HOOK_POSTUNINSTALL\n   MessageBox MB_OK \"PostUninstall\"\n !macroend\n\n ```", "type": ["string", "null"]}, "minimumWebview2Version": {"description": "Try to ensure that the WebView2 version is equal to or newer than this version,\n if the user's WebView2 is older than this version,\n the installer will try to trigger a WebView2 update.", "type": ["string", "null"]}}, "additionalProperties": false}, "NSISInstallerMode": {"description": "Install Modes for the NSIS installer.", "oneOf": [{"description": "Default mode for the installer.\n\n Install the app by default in a directory that doesn't require Administrator access.\n\n Installer metadata will be saved under the `HKCU` registry path.", "type": "string", "enum": ["currentUser"]}, {"description": "Install the app by default in the `Program Files` folder directory requires Administrator\n access for the installation.\n\n Installer metadata will be saved under the `HKLM` registry path.", "type": "string", "enum": ["perMachine"]}, {"description": "Combines both modes and allows the user to choose at install time\n whether to install for the current user or per machine. Note that this mode\n will require Administrator access even if the user wants to install it for the current user only.\n\n Installer metadata will be saved under the `HKLM` or `HKCU` registry path based on the user's choice.", "type": "string", "enum": ["both"]}]}, "NsisCompression": {"description": "Compression algorithms used in the NSIS installer.\n\n See <https://nsis.sourceforge.io/Reference/SetCompressor>", "oneOf": [{"description": "ZLIB uses the deflate algorithm, it is a quick and simple method. With the default compression level it uses about 300 KB of memory.", "type": "string", "enum": ["zlib"]}, {"description": "BZIP2 usually gives better compression ratios than ZLIB, but it is a bit slower and uses more memory. With the default compression level it uses about 4 MB of memory.", "type": "string", "enum": ["bzip2"]}, {"description": "LZMA (default) is a new compression method that gives very good compression ratios. The decompression speed is high (10-20 MB/s on a 2 GHz CPU), the compression speed is lower. The memory size that will be used for decompression is the dictionary size plus a few KBs, the default is 8 MB.", "type": "string", "enum": ["lzma"]}, {"description": "Disable compression", "type": "string", "enum": ["none"]}]}, "CustomSignCommandConfig": {"description": "Custom Signing Command configuration.", "anyOf": [{"description": "A string notation of the script to execute.\n\n \"%1\" will be replaced with the path to the binary to be signed.\n\n This is a simpler notation for the command.\n <PERSON><PERSON> will split the string with `' '` and use the first element as the command name and the rest as arguments.\n\n If you need to use whitespace in the command or arguments, use the object notation [`Self::ScriptWithOptions`].", "type": "string"}, {"description": "An object notation of the command.\n\n This is more complex notation for the command but\n this allows you to use whitespace in the command and arguments.", "type": "object", "required": ["args", "cmd"], "properties": {"cmd": {"description": "The command to run to sign the binary.", "type": "string"}, "args": {"description": "The arguments to pass to the command.\n\n \"%1\" will be replaced with the path to the binary to be signed.", "type": "array", "items": {"type": "string"}}}, "additionalProperties": false}]}, "LinuxConfig": {"description": "Configuration for Linux bundles.\n\n See more: <https://v2.tauri.app/reference/config/#linuxconfig>", "type": "object", "properties": {"appimage": {"description": "Configuration for the AppImage bundle.", "default": {"bundleMediaFramework": false, "files": {}}, "allOf": [{"$ref": "#/definitions/AppImageConfig"}]}, "deb": {"description": "Configuration for the Debian bundle.", "default": {"files": {}}, "allOf": [{"$ref": "#/definitions/DebConfig"}]}, "rpm": {"description": "Configuration for the RPM bundle.", "default": {"epoch": 0, "files": {}, "release": "1"}, "allOf": [{"$ref": "#/definitions/RpmConfig"}]}}, "additionalProperties": false}, "AppImageConfig": {"description": "Configuration for AppImage bundles.\n\n See more: <https://v2.tauri.app/reference/config/#appimageconfig>", "type": "object", "properties": {"bundleMediaFramework": {"description": "Include additional gstreamer dependencies needed for audio and video playback.\n This increases the bundle size by ~15-35MB depending on your build system.", "default": false, "type": "boolean"}, "files": {"description": "The files to include in the Appimage Binary.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}}, "additionalProperties": false}, "DebConfig": {"description": "Configuration for Debian (.deb) bundles.\n\n See more: <https://v2.tauri.app/reference/config/#debconfig>", "type": "object", "properties": {"depends": {"description": "The list of deb dependencies your application relies on.", "type": ["array", "null"], "items": {"type": "string"}}, "recommends": {"description": "The list of deb dependencies your application recommends.", "type": ["array", "null"], "items": {"type": "string"}}, "provides": {"description": "The list of dependencies the package provides.", "type": ["array", "null"], "items": {"type": "string"}}, "conflicts": {"description": "The list of package conflicts.", "type": ["array", "null"], "items": {"type": "string"}}, "replaces": {"description": "The list of package replaces.", "type": ["array", "null"], "items": {"type": "string"}}, "files": {"description": "The files to include on the package.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}, "section": {"description": "Define the section in Debian Control file. See : https://www.debian.org/doc/debian-policy/ch-archive.html#s-subsections", "type": ["string", "null"]}, "priority": {"description": "Change the priority of the Debian Package. By default, it is set to `optional`.\n Recognized Priorities as of now are :  `required`, `important`, `standard`, `optional`, `extra`", "type": ["string", "null"]}, "changelog": {"description": "Path of the uncompressed Changelog file, to be stored at /usr/share/doc/package-name/changelog.gz. See\n <https://www.debian.org/doc/debian-policy/ch-docs.html#changelog-files-and-release-notes>", "type": ["string", "null"]}, "desktopTemplate": {"description": "Path to a custom desktop file Handlebars template.\n\n Available variables: `categories`, `comment` (optional), `exec`, `icon` and `name`.", "type": ["string", "null"]}, "preInstallScript": {"description": "Path to script that will be executed before the package is unpacked. See\n <https://www.debian.org/doc/debian-policy/ch-maintainerscripts.html>", "type": ["string", "null"]}, "postInstallScript": {"description": "Path to script that will be executed after the package is unpacked. See\n <https://www.debian.org/doc/debian-policy/ch-maintainerscripts.html>", "type": ["string", "null"]}, "preRemoveScript": {"description": "Path to script that will be executed before the package is removed. See\n <https://www.debian.org/doc/debian-policy/ch-maintainerscripts.html>", "type": ["string", "null"]}, "postRemoveScript": {"description": "Path to script that will be executed after the package is removed. See\n <https://www.debian.org/doc/debian-policy/ch-maintainerscripts.html>", "type": ["string", "null"]}}, "additionalProperties": false}, "RpmConfig": {"description": "Configuration for RPM bundles.", "type": "object", "properties": {"depends": {"description": "The list of RPM dependencies your application relies on.", "type": ["array", "null"], "items": {"type": "string"}}, "recommends": {"description": "The list of RPM dependencies your application recommends.", "type": ["array", "null"], "items": {"type": "string"}}, "provides": {"description": "The list of RPM dependencies your application provides.", "type": ["array", "null"], "items": {"type": "string"}}, "conflicts": {"description": "The list of RPM dependencies your application conflicts with. They must not be present\n in order for the package to be installed.", "type": ["array", "null"], "items": {"type": "string"}}, "obsoletes": {"description": "The list of RPM dependencies your application supersedes - if this package is installed,\n packages listed as \"obsoletes\" will be automatically removed (if they are present).", "type": ["array", "null"], "items": {"type": "string"}}, "release": {"description": "The RPM release tag.", "default": "1", "type": "string"}, "epoch": {"description": "The RPM epoch.", "default": 0, "type": "integer", "format": "uint32", "minimum": 0.0}, "files": {"description": "The files to include on the package.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}, "desktopTemplate": {"description": "Path to a custom desktop file Handlebars template.\n\n Available variables: `categories`, `comment` (optional), `exec`, `icon` and `name`.", "type": ["string", "null"]}, "preInstallScript": {"description": "Path to script that will be executed before the package is unpacked. See\n <http://ftp.rpm.org/max-rpm/s1-rpm-inside-scripts.html>", "type": ["string", "null"]}, "postInstallScript": {"description": "Path to script that will be executed after the package is unpacked. See\n <http://ftp.rpm.org/max-rpm/s1-rpm-inside-scripts.html>", "type": ["string", "null"]}, "preRemoveScript": {"description": "Path to script that will be executed before the package is removed. See\n <http://ftp.rpm.org/max-rpm/s1-rpm-inside-scripts.html>", "type": ["string", "null"]}, "postRemoveScript": {"description": "Path to script that will be executed after the package is removed. See\n <http://ftp.rpm.org/max-rpm/s1-rpm-inside-scripts.html>", "type": ["string", "null"]}, "compression": {"description": "Compression algorithm and level. Defaults to `Gzip` with level 6.", "anyOf": [{"$ref": "#/definitions/RpmCompression"}, {"type": "null"}]}}, "additionalProperties": false}, "RpmCompression": {"description": "Compression algorithms used when bundling RPM packages.", "oneOf": [{"description": "Gzip compression", "type": "object", "required": ["level", "type"], "properties": {"type": {"type": "string", "enum": ["gzip"]}, "level": {"description": "Gzip compression level", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, {"description": "Zstd compression", "type": "object", "required": ["level", "type"], "properties": {"type": {"type": "string", "enum": ["zstd"]}, "level": {"description": "Zstd compression level", "type": "integer", "format": "int32"}}, "additionalProperties": false}, {"description": "Xz compression", "type": "object", "required": ["level", "type"], "properties": {"type": {"type": "string", "enum": ["xz"]}, "level": {"description": "Xz compression level", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, {"description": "Bzip2 compression", "type": "object", "required": ["level", "type"], "properties": {"type": {"type": "string", "enum": ["bzip2"]}, "level": {"description": "Bzip2 compression level", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, {"description": "Disable compression", "type": "object", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["none"]}}, "additionalProperties": false}]}, "MacConfig": {"description": "Configuration for the macOS bundles.\n\n See more: <https://v2.tauri.app/reference/config/#macconfig>", "type": "object", "properties": {"frameworks": {"description": "A list of strings indicating any macOS X frameworks that need to be bundled with the application.\n\n If a name is used, \".framework\" must be omitted and it will look for standard install locations. You may also use a path to a specific framework.", "type": ["array", "null"], "items": {"type": "string"}}, "files": {"description": "The files to include in the application relative to the Contents directory.", "default": {}, "type": "object", "additionalProperties": {"type": "string"}}, "bundleVersion": {"description": "The version of the build that identifies an iteration of the bundle.\n\n Translates to the bundle's CFBundleVersion property.", "type": ["string", "null"]}, "bundleName": {"description": "The name of the builder that built the bundle.\n\n Translates to the bundle's CFBundleName property.\n\n If not set, defaults to the package's product name.", "type": ["string", "null"]}, "minimumSystemVersion": {"description": "A version string indicating the minimum macOS X version that the bundled application supports. Defaults to `10.13`.\n\n Setting it to `null` completely removes the `LSMinimumSystemVersion` field on the bundle's `Info.plist`\n and the `MACOSX_DEPLOYMENT_TARGET` environment variable.\n\n An empty string is considered an invalid value so the default value is used.", "default": "10.13", "type": ["string", "null"]}, "exceptionDomain": {"description": "Allows your application to communicate with the outside world.\n It should be a lowercase, without port and protocol domain name.", "type": ["string", "null"]}, "signingIdentity": {"description": "Identity to use for code signing.", "type": ["string", "null"]}, "hardenedRuntime": {"description": "Whether the codesign should enable [hardened runtime](https://developer.apple.com/documentation/security/hardened_runtime) (for executables) or not.", "default": true, "type": "boolean"}, "providerShortName": {"description": "Provider short name for notarization.", "type": ["string", "null"]}, "entitlements": {"description": "Path to the entitlements file.", "type": ["string", "null"]}, "dmg": {"description": "DMG-specific settings.", "default": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"height": 400, "width": 660}}, "allOf": [{"$ref": "#/definitions/DmgConfig"}]}}, "additionalProperties": false}, "DmgConfig": {"description": "Configuration for Apple Disk Image (.dmg) bundles.\n\n See more: <https://v2.tauri.app/reference/config/#dmgconfig>", "type": "object", "properties": {"background": {"description": "Image to use as the background in dmg file. Accepted formats: `png`/`jpg`/`gif`.", "type": ["string", "null"]}, "windowPosition": {"description": "Position of volume window on screen.", "anyOf": [{"$ref": "#/definitions/Position"}, {"type": "null"}]}, "windowSize": {"description": "Size of volume window.", "default": {"height": 400, "width": 660}, "allOf": [{"$ref": "#/definitions/Size"}]}, "appPosition": {"description": "Position of app file on window.", "default": {"x": 180, "y": 170}, "allOf": [{"$ref": "#/definitions/Position"}]}, "applicationFolderPosition": {"description": "Position of application folder on window.", "default": {"x": 480, "y": 170}, "allOf": [{"$ref": "#/definitions/Position"}]}}, "additionalProperties": false}, "Position": {"description": "Position coordinates struct.", "type": "object", "required": ["x", "y"], "properties": {"x": {"description": "X coordinate.", "type": "integer", "format": "uint32", "minimum": 0.0}, "y": {"description": "Y coordinate.", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, "Size": {"description": "Size of the window.", "type": "object", "required": ["height", "width"], "properties": {"width": {"description": "Width of the window.", "type": "integer", "format": "uint32", "minimum": 0.0}, "height": {"description": "Height of the window.", "type": "integer", "format": "uint32", "minimum": 0.0}}, "additionalProperties": false}, "IosConfig": {"description": "General configuration for the iOS target.", "type": "object", "properties": {"template": {"description": "A custom [XcodeGen] project.yml template to use.\n\n [XcodeGen]: <https://github.com/yonaskolb/XcodeGen>", "type": ["string", "null"]}, "frameworks": {"description": "A list of strings indicating any iOS frameworks that need to be bundled with the application.\n\n Note that you need to recreate the iOS project for the changes to be applied.", "type": ["array", "null"], "items": {"type": "string"}}, "developmentTeam": {"description": "The development team. This value is required for iOS development because code signing is enforced.\n The `APPLE_DEVELOPMENT_TEAM` environment variable can be set to overwrite it.", "type": ["string", "null"]}, "bundleVersion": {"description": "The version of the build that identifies an iteration of the bundle.\n\n Translates to the bundle's CFBundleVersion property.", "type": ["string", "null"]}, "minimumSystemVersion": {"description": "A version string indicating the minimum iOS version that the bundled application supports. Defaults to `13.0`.\n\n Maps to the IPHONEOS_DEPLOYMENT_TARGET value.", "default": "13.0", "type": "string"}}, "additionalProperties": false}, "AndroidConfig": {"description": "General configuration for the Android target.", "type": "object", "properties": {"minSdkVersion": {"description": "The minimum API level required for the application to run.\n The Android system will prevent the user from installing the application if the system's API level is lower than the value specified.", "default": 24, "type": "integer", "format": "uint32", "minimum": 0.0}, "versionCode": {"description": "The version code of the application.\n It is limited to 2,100,000,000 as per Google Play Store requirements.\n\n By default we use your configured version and perform the following math:\n versionCode = version.major * 1000000 + version.minor * 1000 + version.patch", "type": ["integer", "null"], "format": "uint32", "maximum": 2100000000.0, "minimum": 1.0}}, "additionalProperties": false}, "PluginConfig": {"description": "The plugin configs holds a HashMap mapping a plugin name to its configuration object.\n\n See more: <https://v2.tauri.app/reference/config/#pluginconfig>", "type": "object", "additionalProperties": true}}}