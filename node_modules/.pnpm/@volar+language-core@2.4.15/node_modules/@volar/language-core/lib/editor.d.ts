import type { CodeInformation, Mapper } from './types';
export declare function isHoverEnabled(info: CodeInformation): boolean;
export declare function isInlayHintsEnabled(info: CodeInformation): boolean;
export declare function isCodeLensEnabled(info: CodeInformation): boolean;
export declare function isMonikerEnabled(info: CodeInformation): boolean;
export declare function isInlineValueEnabled(info: CodeInformation): boolean;
export declare function isSemanticTokensEnabled(info: CodeInformation): boolean;
export declare function isCallHierarchyEnabled(info: CodeInformation): boolean;
export declare function isTypeHierarchyEnabled(info: CodeInformation): boolean;
export declare function isRenameEnabled(info: CodeInformation): boolean;
export declare function isDefinitionEnabled(info: CodeInformation): boolean;
export declare function isTypeDefinitionEnabled(info: CodeInformation): boolean;
export declare function isReferencesEnabled(info: CodeInformation): boolean;
export declare function isImplementationEnabled(info: CodeInformation): boolean;
export declare function isHighlightEnabled(info: CodeInformation): boolean;
export declare function isSymbolsEnabled(info: CodeInformation): boolean;
export declare function isFoldingRangesEnabled(info: CodeInformation): boolean;
export declare function isSelectionRangesEnabled(info: CodeInformation): boolean;
export declare function isLinkedEditingEnabled(info: CodeInformation): boolean;
export declare function isColorEnabled(info: CodeInformation): boolean;
export declare function isDocumentLinkEnabled(info: CodeInformation): boolean;
export declare function isDiagnosticsEnabled(info: CodeInformation): boolean;
export declare function isCodeActionsEnabled(info: CodeInformation): boolean;
export declare function isFormattingEnabled(info: CodeInformation): boolean;
export declare function isCompletionEnabled(info: CodeInformation): boolean;
export declare function isAutoInsertEnabled(info: CodeInformation): boolean;
export declare function isSignatureHelpEnabled(info: CodeInformation): boolean;
export declare function shouldReportDiagnostics(info: CodeInformation, source: string | undefined, code: string | number | undefined): boolean;
export declare function resolveRenameNewName(newName: string, info: CodeInformation): string;
export declare function resolveRenameEditText(text: string, info: CodeInformation): string;
export declare function findOverlapCodeRange(start: number, end: number, map: Mapper, filter: (data: CodeInformation) => boolean): {
    start: number;
    end: number;
} | undefined;
